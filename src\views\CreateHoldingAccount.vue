<template>
  <div class="create-holding-page">
    <van-nav-bar
      title="创建持仓账本"
      left-arrow
      @click-left="onBack"
      right-text="..."
      class="nav-bar"
    />
    
    <div class="page-content">
      <!-- 步骤进度条 -->
      <div class="progress-section">
        <van-steps :active="currentStep" active-color="#ff6b6b" inactive-color="#e5e5e5">
          <van-step>设置投资目标</van-step>
          <van-step>选择持仓产品</van-step>
          <van-step>完成</van-step>
        </van-steps>
      </div>
      
      <!-- 账本信息 -->
      <div class="account-info">
        <div class="info-row">
          <span class="info-label">账本名称</span>
          <span class="info-value">{{ accountName }}</span>
        </div>
      </div>
      
      <!-- 投资期限选择 -->
      <div class="investment-period-section">
        <div class="section-header">
          <div class="step-number">1/3</div>
          <h3 class="section-title">您计划的投资期限是多久？</h3>
        </div>
        
        <div class="period-options">
          <div 
            v-for="option in periodOptions" 
            :key="option.value"
            class="period-option"
            :class="{ 'selected': selectedPeriod === option.value }"
            @click="selectPeriod(option.value)"
          >
            <div class="option-radio">
              <div class="radio-inner" v-if="selectedPeriod === option.value"></div>
            </div>
            <span class="option-text">{{ option.label }}</span>
          </div>
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="bottom-action">
        <van-button 
          block 
          class="next-btn"
          :disabled="!selectedPeriod"
          @click="onNext"
        >
          下一步
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import {
  getFormData,
  updateStepData,
  clearFormData,
  initFormData,
  getStepData
} from '../utils/formDataManager.js'

const router = useRouter()

// 当前步骤
const currentStep = ref(0)

// 投资期限选项
const periodOptions = [
  { value: '6months', label: '计划持有【6个月以内】' },
  { value: '6-12months', label: '计划持有【6-12个月】' },
  { value: '1-3years', label: '计划持有【1-3年】' },
  { value: '3-5years', label: '计划持有【3-5年】' },
  { value: '5years+', label: '计划持有【5年以上】' }
]

// 选中的投资期限
const selectedPeriod = ref('')

// 计算属性：账本名称
const accountName = computed(() => {
  const formData = getFormData()
  return formData.accountName || '持仓账本#********'
})

// 选择投资期限
const selectPeriod = (value) => {
  selectedPeriod.value = value
  // 保存到步骤1数据
  updateStepData(1, { investmentPeriod: value })
}

// 返回上一页
const onBack = () => {
  // 清除表单数据
  clearFormData()
  router.back()
}

// 下一步
const onNext = () => {
  if (!selectedPeriod.value) {
    showToast('请选择投资期限')
    return
  }

  // 保存当前步骤数据
  updateStepData(1, { investmentPeriod: selectedPeriod.value })

  // 跳转到下一步（选择持仓产品页面）
  showToast('下一步功能待实现')
  console.log('当前表单数据：', getFormData())

  // router.push({
  //   name: 'SelectHoldingProducts',
  //   query: { step: 2 }
  // })
}

// 初始化页面数据
onMounted(() => {
  // 初始化表单数据
  initFormData('holding')

  // 加载已保存的步骤1数据
  const step1Data = getStepData(1)
  if (step1Data.investmentPeriod) {
    selectedPeriod.value = step1Data.investmentPeriod
  }
})
</script>

<style scoped>
/* 页面容器 */
.create-holding-page {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.nav-bar {
  background: #ff6b6b !important;
  color: #fff;
  flex-shrink: 0;
}

/* 页面内容 */
.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 100px;
}

/* 步骤进度条 */
.progress-section {
  background: #fff;
  padding: 5% 4%;
  margin-bottom: 3%;
}

/* 账本信息 */
.account-info {
  background: #fff;
  padding: 4%;
  margin-bottom: 3%;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 投资期限选择区域 */
.investment-period-section {
  background: #fff;
  padding: 5% 4%;
  flex: 1;
}

.section-header {
  margin-bottom: 6%;
}

.step-number {
  font-size: 16px;
  color: #ff6b6b;
  font-weight: 600;
  margin-bottom: 2%;
}

.section-title {
  font-size: 18px;
  color: #333;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
}

/* 期限选项 */
.period-options {
  display: flex;
  flex-direction: column;
  gap: 4%;
}

.period-option {
  display: flex;
  align-items: center;
  padding: 4% 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.period-option:active {
  opacity: 0.7;
}

.option-radio {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 50%;
  margin-right: 4%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.period-option.selected .option-radio {
  border-color: #ff6b6b;
}

.radio-inner {
  width: 10px;
  height: 10px;
  background: #ff6b6b;
  border-radius: 50%;
}

.option-text {
  font-size: 16px;
  color: #333;
  line-height: 1.4;
}

.period-option.selected .option-text {
  color: #ff6b6b;
  font-weight: 500;
}

/* 底部按钮 */
.bottom-action {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 4% 4% 6% 4%;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
  border-top: 1px solid #f0f0f0;
}

.next-btn {
  background: #ff6b6b !important;
  border: none !important;
  border-radius: 8px !important;
  height: 48px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.next-btn:disabled {
  background: #ddd !important;
  box-shadow: none !important;
  color: #999 !important;
}

.next-btn:not(:disabled):active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(255, 107, 107, 0.4);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .progress-section {
    padding: 6% 5%;
  }
  
  .investment-period-section {
    padding: 6% 5%;
  }
  
  .section-title {
    font-size: 17px;
  }
  
  .option-text {
    font-size: 15px;
  }
  
  .bottom-action {
    padding: 5% 5% 8% 5%;
  }
}

@media (min-width: 768px) {
  .page-content {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .progress-section,
  .account-info,
  .investment-period-section {
    margin-left: 4%;
    margin-right: 4%;
    border-radius: 12px;
  }
  
  .bottom-action {
    max-width: 600px;
    margin: 0 auto;
    left: 50%;
    transform: translateX(-50%);
    padding: 3% 6% 4% 6%;
  }
}

/* 导航栏样式优化 */
:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__text) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}

/* 步骤条样式优化 */
:deep(.van-step__title) {
  font-size: 13px;
  color: #666;
}

:deep(.van-step__title--active) {
  color: #ff6b6b;
  font-weight: 500;
}
</style>
