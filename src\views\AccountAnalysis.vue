<template>
  <div class="analysis-page">
    <van-nav-bar
      title="我的持仓账本诊断"
      left-arrow
      @click-left="onBack"
      right-text="..."
      class="nav-bar"
    />
    
    <div class="page-content">
      <!-- 标签页 -->
      <div class="tabs-section">
        <van-tabs v-model:active="activeTab" class="analysis-tabs">
          <van-tab title="资产配置" name="asset"></van-tab>
          <van-tab title="收益体验" name="return"></van-tab>
          <van-tab title="风险能力" name="risk"></van-tab>
          <van-tab title="基金诊断" name="fund"></van-tab>
        </van-tabs>
      </div>

      <!-- 资产配置页面 -->
      <div v-if="activeTab === 'asset'" class="tab-content">
        <!-- 资产大类分布 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">资产大类分布</h3>
            <div class="toggle-section">
              <span class="toggle-label">持仓穿透</span>
              <van-switch v-model="holdingPenetration" size="20px" />
            </div>
          </div>
          
          <!-- 饼图区域 -->
          <div class="chart-container">
            <div class="pie-chart">
              <canvas ref="pieCanvas" width="200" height="200"></canvas>
            </div>
            <div class="chart-legend">
              <div class="legend-item" v-for="item in assetDistribution" :key="item.name">
                <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                <span class="legend-name">{{ item.name }}</span>
                <span class="legend-value">{{ item.percentage }}</span>
              </div>
            </div>
          </div>
          
          <!-- 分析文本 -->
          <div class="analysis-text">
            <p>股票类资产占比过高</p>
            <p>当前股票类资产占比高达60%，债券资产占比仅为10%，希望您的风险承受能力等级较高，当前风险类资产配置偏高，可能会带来超出您的承受能力的风险。</p>
          </div>
        </div>

        <!-- 行业分布 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">行业分布</h3>
            <div class="view-toggle">
              <span class="toggle-text">顶风险</span>
              <van-icon name="arrow-up" />
            </div>
          </div>
          
          <div class="industry-subtitle">
            <span>前五最新报告期</span>
          </div>
          
          <!-- 行业分布图表 -->
          <div class="industry-chart">
            <div class="industry-bars">
              <div class="industry-bar" v-for="industry in industryData" :key="industry.name">
                <div class="bar-container">
                  <div class="bar-fill" :style="{ 
                    width: industry.percentage, 
                    backgroundColor: industry.color 
                  }"></div>
                  <div class="bar-info">
                    <span class="industry-name">{{ industry.name }}</span>
                    <span class="industry-percentage">{{ industry.percentage }}</span>
                  </div>
                </div>
                <div class="industry-change" :class="industry.changeClass">
                  {{ industry.change }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- 行业分析 -->
          <div class="industry-analysis">
            <div class="analysis-highlight">
              <span class="highlight-text">交通运输</span>
              <span class="analysis-desc">行业股本里的以下基金持有</span>
            </div>
            
            <div class="fund-list">
              <div class="fund-item">
                <span class="fund-name">易方达蓝筹精选混合</span>
                <div class="fund-progress">
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 62%"></div>
                  </div>
                  <span class="fund-percentage">62.46%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 收益表现页面 -->
      <div v-if="activeTab === 'return'" class="tab-content">
        <!-- 回测收益表现 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">回测收益表现</h3>
            <div class="view-toggle">
              <span class="toggle-text">1 顶风险</span>
              <van-icon name="arrow-up" />
            </div>
          </div>

          <!-- 客户期望年化收益 -->
          <div class="expected-return">
            <span class="expected-label">客户期望年化收益</span>
            <span class="expected-value">12%</span>
          </div>

          <!-- 收益指标 -->
          <div class="return-indicators">
            <div class="indicator-item">
              <div class="indicator-dot" style="background: #ff4d4f;"></div>
              <span class="indicator-label">本组合</span>
              <span class="indicator-value positive">3.44%</span>
            </div>
            <div class="indicator-item">
              <div class="indicator-dot" style="background: #1890ff;"></div>
              <span class="indicator-label">沪深300</span>
              <span class="indicator-value positive">3.33%</span>
            </div>
            <div class="indicator-item">
              <div class="indicator-dot" style="background: #faad14;"></div>
              <span class="indicator-label">中证全债</span>
              <span class="indicator-value positive">1.33%</span>
            </div>
          </div>

          <div class="return-period">
            <span class="period-label">业绩表现</span>
            <span class="period-value">12.77%</span>
            <span class="period-label">最大回撤</span>
            <span class="period-value">12.77%</span>
          </div>

          <!-- 收益曲线图 -->
          <div class="chart-container">
            <canvas ref="returnChart" width="350" height="200" class="return-chart"></canvas>
          </div>

          <!-- 时间选择器 -->
          <div class="time-selector">
            <div class="time-tabs">
              <span
                v-for="period in timePeriods"
                :key="period.value"
                :class="['time-tab', { active: selectedPeriod === period.value }]"
                @click="selectPeriod(period.value)"
              >
                {{ period.label }}
              </span>
            </div>
          </div>

          <!-- 历史阶段收益率表现及达标期 -->
          <div class="historical-analysis">
            <h4 class="analysis-title">历史阶段收益率表现及达标期</h4>
            <p class="analysis-desc">
              根据历史数据分析，假设客户在1年的持有期组合基金等
              权重，达不到客户的期望年化收益率3.44%的年化收
              益率，达不到客户的期望年化收益目标。
            </p>
          </div>
        </div>

        <!-- 历史盈利概率 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">历史盈利概率</h3>
            <div class="view-toggle">
              <span class="toggle-text">1 顶风险</span>
              <van-icon name="arrow-up" />
            </div>
          </div>

          <p class="probability-desc">
            该组合近5年，在任意时点买入并持有不同时间下的盈利概率
          </p>

          <!-- 概率指标 -->
          <div class="probability-indicators">
            <div class="prob-indicator">
              <div class="prob-dot" style="background: #1890ff;"></div>
              <span class="prob-label">盈利概率 %</span>
            </div>
            <div class="prob-indicator">
              <div class="prob-dot" style="background: #faad14;"></div>
              <span class="prob-label">平均收益率 %</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 风控能力页面 -->
      <div v-if="activeTab === 'risk'" class="tab-content">
        <!-- 风控能力 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">风控能力</h3>
            <div class="view-toggle">
              <span class="toggle-text">1 顶风险</span>
              <van-icon name="arrow-up" />
            </div>
          </div>

          <!-- 最大回撤 -->
          <div class="max-drawdown-section">
            <h4 class="subsection-title">最大回撤</h4>

            <!-- 回撤指标 -->
            <div class="drawdown-indicators">
              <div class="drawdown-item">
                <div class="indicator-dot" style="background: #ff4d4f;"></div>
                <span class="indicator-label">本组合</span>
                <span class="indicator-value">-10%</span>
              </div>
              <div class="drawdown-item">
                <div class="indicator-dot" style="background: #1890ff;"></div>
                <span class="indicator-label">沪深300</span>
                <span class="indicator-value">-5.33%</span>
              </div>
            </div>

            <!-- 回撤曲线图 -->
            <div class="chart-container">
              <canvas ref="drawdownChart" width="350" height="200" class="drawdown-chart"></canvas>
            </div>

            <!-- 时间选择器 -->
            <div class="time-selector">
              <div class="time-tabs">
                <span
                  v-for="period in timePeriods"
                  :key="period.value"
                  :class="['time-tab', { active: selectedRiskPeriod === period.value }]"
                  @click="selectRiskPeriod(period.value)"
                >
                  {{ period.label }}
                </span>
              </div>
            </div>
          </div>

          <!-- 年化波动率 -->
          <div class="volatility-section">
            <h4 class="subsection-title">年化波动率</h4>

            <!-- 波动率指标 -->
            <div class="volatility-indicators">
              <div class="vol-indicator">
                <div class="indicator-dot" style="background: #ff4d4f;"></div>
                <span class="indicator-label">本组合 %</span>
              </div>
              <div class="vol-indicator">
                <div class="indicator-dot" style="background: #1890ff;"></div>
                <span class="indicator-label">沪深300 %</span>
              </div>
            </div>

            <!-- 柱状图 -->
            <div class="volatility-chart">
              <div class="chart-bars">
                <div v-for="(item, index) in volatilityData" :key="index" class="bar-group">
                  <div class="bar-container">
                    <div class="bar red-bar" :style="{ height: item.portfolio + '%' }">
                      <span class="bar-value">{{ item.portfolioValue }}</span>
                    </div>
                    <div class="bar blue-bar" :style="{ height: item.index + '%' }">
                      <span class="bar-value">{{ item.indexValue }}</span>
                    </div>
                  </div>
                  <div class="bar-label">{{ item.period }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 历史年化波动率状况 -->
          <div class="historical-volatility">
            <h4 class="analysis-title">历史年化波动率状况</h4>
            <p class="analysis-desc">
              根据3年的历史数据分析，近1年化波动率较高，超过
              12.44%，低于客户的风险承受能力。
            </p>
          </div>
        </div>
      </div>

      <!-- 基金诊断页面 -->
      <div v-if="activeTab === 'fund'" class="tab-content">
        <!-- 诊断提醒标签 -->
        <div class="diagnosis-alerts">
          <div class="alert-tag warning">
            <span class="alert-number">3</span>
            <span class="alert-text">易方达博享定开</span>
          </div>
          <div class="alert-tag danger">
            <span class="alert-number">2</span>
            <span class="alert-text">融通健康长寿混合A</span>
          </div>
        </div>

        <!-- 收益表现 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">收益表现</h3>
            <div class="view-toggle">
              <span class="toggle-text">1 顶风险</span>
              <van-icon name="arrow-up" />
            </div>
          </div>

          <!-- 收益指标 -->
          <div class="fund-indicators">
            <div class="fund-indicator-item">
              <div class="indicator-dot" style="background: #ff4d4f;"></div>
              <span class="indicator-label">本基金</span>
              <span class="indicator-value positive">3.44%</span>
            </div>
            <div class="fund-indicator-item">
              <div class="indicator-dot" style="background: #1890ff;"></div>
              <span class="indicator-label">业绩基准</span>
              <span class="indicator-value positive">1.33%</span>
            </div>
            <div class="fund-indicator-item">
              <div class="indicator-dot" style="background: #faad14;"></div>
              <span class="indicator-label">沪深300</span>
              <span class="indicator-value positive">3.11%</span>
            </div>
          </div>

          <div class="fund-period">
            <span class="period-label">业绩表现</span>
            <span class="period-value">12.77%</span>
            <span class="period-label">最大回撤</span>
            <span class="period-value">12.33%</span>
          </div>

          <!-- 基金收益曲线图 -->
          <div class="chart-container">
            <canvas ref="fundChart" width="350" height="200" class="fund-chart"></canvas>
          </div>

          <!-- 时间选择器 -->
          <div class="time-selector">
            <div class="time-tabs">
              <span
                v-for="period in fundTimePeriods"
                :key="period.value"
                :class="['time-tab', { active: selectedFundPeriod === period.value }]"
                @click="selectFundPeriod(period.value)"
              >
                {{ period.label }}
              </span>
            </div>
          </div>

          <!-- 阶段收益表格 -->
          <div class="performance-table">
            <div class="table-header">
              <div class="header-cell">阶段</div>
              <div class="header-cell">涨跌幅</div>
              <div class="header-cell">同类平均</div>
              <div class="header-cell">同类排名</div>
            </div>
            <div v-for="(row, index) in performanceData" :key="index" class="table-row">
              <div class="table-cell period-cell">{{ row.period }}</div>
              <div class="table-cell" :class="getValueClass(row.change)">{{ row.change }}</div>
              <div class="table-cell" :class="getValueClass(row.average)">{{ row.average }}</div>
              <div class="table-cell ranking-cell">{{ row.ranking }}</div>
            </div>
          </div>

          <!-- 近期综合收益能力较弱 -->
          <div class="performance-analysis">
            <h4 class="analysis-title">近期综合收益能力较弱</h4>
            <p class="analysis-desc">
              该基金近期的综合收益能力较弱，落后于60%的同类基金。
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 当前活跃标签
const activeTab = ref('asset')

// 持仓穿透开关
const holdingPenetration = ref(true)

// 饼图画布引用
const pieCanvas = ref(null)

// 收益图表画布引用
const returnChart = ref(null)

// 选中的时间周期
const selectedPeriod = ref('近2年')

// 时间周期选项
const timePeriods = ref([
  { label: '近6月', value: '近6月' },
  { label: '近1年', value: '近1年' },
  { label: '近2年', value: '近2年' },
  { label: '近3年', value: '近3年' },
  { label: '近5年', value: '近5年' }
])

// 风控页面相关数据
const drawdownChart = ref(null)
const selectedRiskPeriod = ref('近2年')

// 年化波动率数据
const volatilityData = ref([
  {
    period: '近1年',
    portfolio: 60,
    index: 80,
    portfolioValue: '8.32',
    indexValue: '12.44'
  },
  {
    period: '近2年',
    portfolio: 60,
    index: 80,
    portfolioValue: '8.32',
    indexValue: '12.44'
  },
  {
    period: '近3年',
    portfolio: 60,
    index: 80,
    portfolioValue: '8.32',
    indexValue: '12.44'
  }
])

// 基金诊断相关数据
const fundChart = ref(null)
const selectedFundPeriod = ref('近2年')

// 基金时间周期选项
const fundTimePeriods = ref([
  { label: '近6月', value: '近6月' },
  { label: '近1年', value: '近1年' },
  { label: '近2年', value: '近2年' },
  { label: '近3年', value: '近3年' },
  { label: '成立以来', value: '成立以来' }
])

// 阶段收益表现数据
const performanceData = ref([
  {
    period: '近1个月',
    change: '3.44%',
    average: '3.44%',
    ranking: '40/890'
  },
  {
    period: '近6个月',
    change: '-3.44%',
    average: '-3.44%',
    ranking: '40/890'
  },
  {
    period: '近1年',
    change: '-3.44%',
    average: '-3.44%',
    ranking: '780/890'
  },
  {
    period: '近2年',
    change: '3.44%',
    average: '3.44%',
    ranking: '40/890'
  }
])

// 资产分布数据
const assetDistribution = ref([
  { name: '股票', percentage: '60.00%', color: '#52c41a' },
  { name: '债券', percentage: '30.00%', color: '#1890ff' },
  { name: '商品', percentage: '7.00%', color: '#faad14' },
  { name: '其他', percentage: '3.00%', color: '#d9d9d9' }
])

// 行业分布数据
const industryData = ref([
  { 
    name: '交通运输', 
    percentage: '52.16%', 
    color: '#ff7875',
    change: '近1月 +0.94%',
    changeClass: 'positive'
  },
  { 
    name: '机械设备', 
    percentage: '21.97%', 
    color: '#73d13d',
    change: '近1月 +4.24%',
    changeClass: 'positive'
  }
])

// 绘制饼图
const drawPieChart = () => {
  if (!pieCanvas.value) return

  const canvas = pieCanvas.value
  const ctx = canvas.getContext('2d')
  const centerX = canvas.width / 2
  const centerY = canvas.height / 2
  const radius = 80

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  let currentAngle = -Math.PI / 2 // 从顶部开始

  assetDistribution.value.forEach(item => {
    const percentage = parseFloat(item.percentage) / 100
    const sliceAngle = percentage * 2 * Math.PI

    // 绘制扇形
    ctx.beginPath()
    ctx.moveTo(centerX, centerY)
    ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
    ctx.closePath()
    ctx.fillStyle = item.color
    ctx.fill()

    // 绘制边框
    ctx.strokeStyle = '#fff'
    ctx.lineWidth = 2
    ctx.stroke()

    currentAngle += sliceAngle
  })

  // 绘制中心圆
  ctx.beginPath()
  ctx.arc(centerX, centerY, 25, 0, 2 * Math.PI)
  ctx.fillStyle = '#fff'
  ctx.fill()
}

// 绘制收益曲线图
const drawReturnChart = () => {
  if (!returnChart.value) return

  const canvas = returnChart.value
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 模拟数据点
  const dataPoints = [
    // 本组合数据 (红色)
    { x: 50, y: 120, color: '#ff4d4f' },
    { x: 80, y: 100, color: '#ff4d4f' },
    { x: 110, y: 80, color: '#ff4d4f' },
    { x: 140, y: 90, color: '#ff4d4f' },
    { x: 170, y: 70, color: '#ff4d4f' },
    { x: 200, y: 85, color: '#ff4d4f' },
    { x: 230, y: 75, color: '#ff4d4f' },
    { x: 260, y: 80, color: '#ff4d4f' },
    { x: 290, y: 85, color: '#ff4d4f' },
    { x: 320, y: 90, color: '#ff4d4f' }
  ]

  const dataPoints2 = [
    // 沪深300数据 (蓝色)
    { x: 50, y: 130, color: '#1890ff' },
    { x: 80, y: 110, color: '#1890ff' },
    { x: 110, y: 140, color: '#1890ff' },
    { x: 140, y: 120, color: '#1890ff' },
    { x: 170, y: 160, color: '#1890ff' },
    { x: 200, y: 150, color: '#1890ff' },
    { x: 230, y: 170, color: '#1890ff' },
    { x: 260, y: 160, color: '#1890ff' },
    { x: 290, y: 165, color: '#1890ff' },
    { x: 320, y: 170, color: '#1890ff' }
  ]

  const dataPoints3 = [
    // 中证全债数据 (黄色)
    { x: 50, y: 110, color: '#faad14' },
    { x: 80, y: 105, color: '#faad14' },
    { x: 110, y: 100, color: '#faad14' },
    { x: 140, y: 95, color: '#faad14' },
    { x: 170, y: 90, color: '#faad14' },
    { x: 200, y: 85, color: '#faad14' },
    { x: 230, y: 80, color: '#faad14' },
    { x: 260, y: 75, color: '#faad14' },
    { x: 290, y: 70, color: '#faad14' },
    { x: 320, y: 65, color: '#faad14' }
  ]

  // 绘制网格线
  ctx.strokeStyle = '#f0f0f0'
  ctx.lineWidth = 1

  // 水平网格线
  for (let i = 0; i <= 4; i++) {
    const y = (height / 4) * i
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()
  }

  // 绘制曲线的函数
  const drawCurve = (points, color) => {
    if (points.length < 2) return

    ctx.strokeStyle = color
    ctx.lineWidth = 2
    ctx.beginPath()

    ctx.moveTo(points[0].x, points[0].y)

    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].x, points[i].y)
    }

    ctx.stroke()
  }

  // 绘制三条曲线
  drawCurve(dataPoints, '#ff4d4f')   // 本组合
  drawCurve(dataPoints2, '#1890ff')  // 沪深300
  drawCurve(dataPoints3, '#faad14')  // 中证全债
}

// 绘制回撤曲线图
const drawDrawdownChart = () => {
  if (!drawdownChart.value) return

  const canvas = drawdownChart.value
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 模拟回撤数据点
  const drawdownPoints1 = [
    // 本组合回撤数据 (红色)
    { x: 30, y: 50, color: '#ff4d4f' },
    { x: 60, y: 60, color: '#ff4d4f' },
    { x: 90, y: 70, color: '#ff4d4f' },
    { x: 120, y: 80, color: '#ff4d4f' },
    { x: 150, y: 90, color: '#ff4d4f' },
    { x: 180, y: 100, color: '#ff4d4f' },
    { x: 210, y: 120, color: '#ff4d4f' },
    { x: 240, y: 140, color: '#ff4d4f' },
    { x: 270, y: 160, color: '#ff4d4f' },
    { x: 300, y: 170, color: '#ff4d4f' },
    { x: 330, y: 50, color: '#ff4d4f' }
  ]

  const drawdownPoints2 = [
    // 沪深300回撤数据 (蓝色)
    { x: 30, y: 60, color: '#1890ff' },
    { x: 60, y: 70, color: '#1890ff' },
    { x: 90, y: 80, color: '#1890ff' },
    { x: 120, y: 90, color: '#1890ff' },
    { x: 150, y: 100, color: '#1890ff' },
    { x: 180, y: 110, color: '#1890ff' },
    { x: 210, y: 130, color: '#1890ff' },
    { x: 240, y: 150, color: '#1890ff' },
    { x: 270, y: 170, color: '#1890ff' },
    { x: 300, y: 180, color: '#1890ff' },
    { x: 330, y: 60, color: '#1890ff' }
  ]

  // 绘制网格线
  ctx.strokeStyle = '#f0f0f0'
  ctx.lineWidth = 1

  // 水平网格线
  for (let i = 0; i <= 5; i++) {
    const y = (height / 5) * i
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()
  }

  // 绘制Y轴标签
  ctx.fillStyle = '#999'
  ctx.font = '10px Arial'
  ctx.textAlign = 'right'

  const yLabels = ['2.21%', '-0.97%', '-22.14%', '-34.37%', '-46.50%']
  yLabels.forEach((label, index) => {
    const y = (height / 5) * index + 5
    ctx.fillText(label, 40, y)
  })

  // 绘制曲线的函数
  const drawCurve = (points, color) => {
    if (points.length < 2) return

    ctx.strokeStyle = color
    ctx.lineWidth = 2
    ctx.beginPath()

    ctx.moveTo(points[0].x, points[0].y)

    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].x, points[i].y)
    }

    ctx.stroke()
  }

  // 绘制两条回撤曲线
  drawCurve(drawdownPoints1, '#ff4d4f')   // 本组合
  drawCurve(drawdownPoints2, '#1890ff')   // 沪深300

  // 绘制X轴时间标签
  ctx.fillStyle = '#999'
  ctx.font = '10px Arial'
  ctx.textAlign = 'center'

  const timeLabels = ['01-01', '01-01', '01-01']
  const timePositions = [100, 200, 300]
  timeLabels.forEach((label, index) => {
    ctx.fillText(label, timePositions[index], height - 5)
  })
}

// 选择时间周期
const selectPeriod = (period) => {
  selectedPeriod.value = period
  // 重新绘制图表
  nextTick(() => {
    drawReturnChart()
  })
}

// 绘制基金收益曲线图
const drawFundChart = () => {
  if (!fundChart.value) return

  const canvas = fundChart.value
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 模拟基金数据点
  const fundPoints = [
    // 本基金数据 (红色)
    { x: 50, y: 120, color: '#ff4d4f' },
    { x: 80, y: 110, color: '#ff4d4f' },
    { x: 110, y: 100, color: '#ff4d4f' },
    { x: 140, y: 90, color: '#ff4d4f' },
    { x: 170, y: 80, color: '#ff4d4f' },
    { x: 200, y: 85, color: '#ff4d4f' },
    { x: 230, y: 75, color: '#ff4d4f' },
    { x: 260, y: 80, color: '#ff4d4f' },
    { x: 290, y: 85, color: '#ff4d4f' },
    { x: 320, y: 90, color: '#ff4d4f' }
  ]

  const benchmarkPoints = [
    // 业绩基准数据 (蓝色)
    { x: 50, y: 130, color: '#1890ff' },
    { x: 80, y: 120, color: '#1890ff' },
    { x: 110, y: 110, color: '#1890ff' },
    { x: 140, y: 100, color: '#1890ff' },
    { x: 170, y: 90, color: '#1890ff' },
    { x: 200, y: 95, color: '#1890ff' },
    { x: 230, y: 85, color: '#1890ff' },
    { x: 260, y: 90, color: '#1890ff' },
    { x: 290, y: 95, color: '#1890ff' },
    { x: 320, y: 100, color: '#1890ff' }
  ]

  const indexPoints = [
    // 沪深300数据 (黄色)
    { x: 50, y: 140, color: '#faad14' },
    { x: 80, y: 130, color: '#faad14' },
    { x: 110, y: 150, color: '#faad14' },
    { x: 140, y: 140, color: '#faad14' },
    { x: 170, y: 160, color: '#faad14' },
    { x: 200, y: 150, color: '#faad14' },
    { x: 230, y: 170, color: '#faad14' },
    { x: 260, y: 160, color: '#faad14' },
    { x: 290, y: 165, color: '#faad14' },
    { x: 320, y: 170, color: '#faad14' }
  ]

  // 绘制网格线
  ctx.strokeStyle = '#f0f0f0'
  ctx.lineWidth = 1

  // 水平网格线
  for (let i = 0; i <= 4; i++) {
    const y = (height / 4) * i
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()
  }

  // 绘制Y轴标签
  ctx.fillStyle = '#999'
  ctx.font = '10px Arial'
  ctx.textAlign = 'right'

  const yLabels = ['6.79%', '0.79%', '-5.21%', '-11.21%', '-17.21%']
  yLabels.forEach((label, index) => {
    const y = (height / 4) * index + 5
    ctx.fillText(label, 40, y)
  })

  // 绘制曲线的函数
  const drawCurve = (points, color) => {
    if (points.length < 2) return

    ctx.strokeStyle = color
    ctx.lineWidth = 2
    ctx.beginPath()

    ctx.moveTo(points[0].x, points[0].y)

    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].x, points[i].y)
    }

    ctx.stroke()
  }

  // 绘制三条曲线
  drawCurve(fundPoints, '#ff4d4f')      // 本基金
  drawCurve(benchmarkPoints, '#1890ff') // 业绩基准
  drawCurve(indexPoints, '#faad14')     // 沪深300

  // 绘制X轴时间标签
  ctx.fillStyle = '#999'
  ctx.font = '10px Arial'
  ctx.textAlign = 'center'

  const timeLabels = ['08-01', '12-01', '04-01']
  const timePositions = [100, 200, 300]
  timeLabels.forEach((label, index) => {
    ctx.fillText(label, timePositions[index], height - 5)
  })
}

// 选择基金时间周期
const selectFundPeriod = (period) => {
  selectedFundPeriod.value = period
  // 重新绘制图表
  nextTick(() => {
    drawFundChart()
  })
}

// 获取数值样式类
const getValueClass = (value) => {
  if (value.startsWith('-')) {
    return 'negative'
  } else if (value !== '0%' && value !== '0.00%') {
    return 'positive'
  }
  return ''
}

// 选择风控时间周期
const selectRiskPeriod = (period) => {
  selectedRiskPeriod.value = period
  // 重新绘制图表
  nextTick(() => {
    drawDrawdownChart()
  })
}

// 返回上一页
const onBack = () => {
  router.back()
}

// 页面挂载后绘制图表
onMounted(() => {
  nextTick(() => {
    drawPieChart()
    drawReturnChart()
    drawDrawdownChart()
    drawFundChart()
  })
})
</script>

<style scoped>
.analysis-page {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.nav-bar {
  background: #ff6b6b !important;
  color: #fff;
  flex-shrink: 0;
}

.page-content {
  flex: 1;
  overflow-y: auto;
}

.tabs-section {
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.analysis-tabs {
  background: #fff;
}

.tab-content {
  padding: 4%;
}

.section-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 4%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4%;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.toggle-section {
  display: flex;
  align-items: center;
  gap: 2%;
}

.toggle-label {
  font-size: 14px;
  color: #666;
}

.view-toggle {
  display: flex;
  align-items: center;
  gap: 1%;
  color: #666;
  font-size: 14px;
}

.chart-container {
  display: flex;
  align-items: center;
  gap: 6%;
  margin-bottom: 4%;
}

.pie-chart {
  flex-shrink: 0;
}

.chart-legend {
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 3%;
  font-size: 14px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 3%;
}

.legend-name {
  flex: 1;
  color: #333;
}

.legend-value {
  color: #666;
  font-weight: 500;
}

.analysis-text {
  padding: 4%;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ff6b6b;
}

.analysis-text p {
  margin: 0 0 2% 0;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

.analysis-text p:first-child {
  font-weight: 600;
  color: #ff6b6b;
}

.industry-subtitle {
  font-size: 12px;
  color: #999;
  margin-bottom: 4%;
}

.industry-chart {
  margin-bottom: 4%;
}

.industry-bar {
  display: flex;
  align-items: center;
  margin-bottom: 4%;
}

.bar-container {
  flex: 1;
  position: relative;
  height: 40px;
  background: #f5f5f5;
  border-radius: 6px;
  margin-right: 3%;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 6px;
  transition: width 0.3s ease;
}

.bar-info {
  position: absolute;
  top: 50%;
  left: 3%;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  width: 94%;
  font-size: 12px;
}

.industry-name {
  color: #333;
  font-weight: 500;
}

.industry-percentage {
  color: #333;
  font-weight: 600;
}

.industry-change {
  font-size: 12px;
  white-space: nowrap;
}

.industry-change.positive {
  color: #52c41a;
}

.industry-change.negative {
  color: #ff4d4f;
}

.industry-analysis {
  padding: 4%;
  background: #f8f9fa;
  border-radius: 8px;
}

.analysis-highlight {
  margin-bottom: 3%;
  font-size: 14px;
}

.highlight-text {
  color: #ff6b6b;
  font-weight: 600;
}

.analysis-desc {
  color: #666;
}

.fund-list {
  margin-top: 3%;
}

.fund-item {
  display: flex;
  align-items: center;
  gap: 3%;
}

.fund-name {
  font-size: 12px;
  color: #333;
  white-space: nowrap;
}

.fund-progress {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 2%;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #333;
  border-radius: 3px;
}

.fund-percentage {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__text) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}

:deep(.van-tabs__nav) {
  background: #fff;
}

:deep(.van-tab) {
  color: #666;
  font-size: 14px;
}

:deep(.van-tab--active) {
  color: #ff6b6b;
  font-weight: 500;
}

:deep(.van-tabs__line) {
  background: #ff6b6b;
}

/* 收益表现页面样式 */
.expected-return {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3% 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 4%;
}

.expected-label {
  font-size: 14px;
  color: #333;
}

.expected-value {
  font-size: 18px;
  font-weight: 600;
  color: #ff6b6b;
}

.return-indicators {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3%;
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: 2%;
  flex: 1;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.indicator-label {
  font-size: 12px;
  color: #666;
}

.indicator-value {
  font-size: 12px;
  font-weight: 500;
}

.indicator-value.positive {
  color: #52c41a;
}

.indicator-value.negative {
  color: #ff4d4f;
}

.return-period {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4%;
  font-size: 12px;
}

.period-label {
  color: #666;
}

.period-value {
  color: #333;
  font-weight: 500;
}

.return-chart {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.time-selector {
  margin: 4% 0;
}

.time-tabs {
  display: flex;
  justify-content: space-between;
  background: #f5f5f5;
  border-radius: 6px;
  padding: 1%;
}

.time-tab {
  flex: 1;
  text-align: center;
  padding: 2% 0;
  font-size: 12px;
  color: #666;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-tab.active {
  background: #fff;
  color: #ff6b6b;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.historical-analysis {
  margin-top: 4%;
  padding: 4%;
  background: #f8f9fa;
  border-radius: 8px;
}

.analysis-title {
  font-size: 14px;
  color: #333;
  font-weight: 600;
  margin: 0 0 3% 0;
}

.analysis-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.probability-desc {
  font-size: 12px;
  color: #666;
  margin: 0 0 4% 0;
  line-height: 1.4;
}

.probability-indicators {
  display: flex;
  gap: 6%;
}

.prob-indicator {
  display: flex;
  align-items: center;
  gap: 2%;
}

.prob-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.prob-label {
  font-size: 12px;
  color: #666;
}

/* 风控能力页面样式 */
.max-drawdown-section {
  margin-bottom: 6%;
}

.subsection-title {
  font-size: 14px;
  color: #333;
  font-weight: 600;
  margin: 0 0 4% 0;
}

.drawdown-indicators {
  display: flex;
  gap: 6%;
  margin-bottom: 4%;
}

.drawdown-item {
  display: flex;
  align-items: center;
  gap: 2%;
}

.drawdown-chart {
  width: 100%;
  height: auto;
  border-radius: 8px;
  margin-bottom: 4%;
}

.volatility-section {
  margin-bottom: 6%;
}

.volatility-indicators {
  display: flex;
  gap: 6%;
  margin-bottom: 4%;
}

.vol-indicator {
  display: flex;
  align-items: center;
  gap: 2%;
}

.volatility-chart {
  margin: 4% 0;
}

.chart-bars {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 150px;
  padding: 0 4%;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.bar-container {
  display: flex;
  align-items: flex-end;
  gap: 3px;
  height: 120px;
  margin-bottom: 2%;
}

.bar {
  width: 20px;
  border-radius: 2px 2px 0 0;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 2px;
}

.red-bar {
  background: #ff4d4f;
}

.blue-bar {
  background: #1890ff;
}

.bar-value {
  font-size: 10px;
  color: #fff;
  font-weight: 500;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.bar-label {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.historical-volatility {
  margin-top: 4%;
  padding: 4%;
  background: #f8f9fa;
  border-radius: 8px;
}

/* 基金诊断页面样式 */
.diagnosis-alerts {
  display: flex;
  gap: 3%;
  margin-bottom: 4%;
}

.alert-tag {
  display: flex;
  align-items: center;
  gap: 2%;
  padding: 2% 3%;
  border-radius: 15px;
  font-size: 12px;
  flex: 1;
}

.alert-tag.warning {
  background: #fff7e6;
  border: 1px solid #ffd591;
}

.alert-tag.danger {
  background: #fff2f0;
  border: 1px solid #ffccc7;
}

.alert-number {
  background: #ff4d4f;
  color: #fff;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
}

.alert-tag.warning .alert-number {
  background: #faad14;
}

.alert-text {
  color: #333;
  font-weight: 500;
}

.fund-indicators {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3%;
}

.fund-indicator-item {
  display: flex;
  align-items: center;
  gap: 2%;
  flex: 1;
}

.fund-period {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4%;
  font-size: 12px;
}

.fund-chart {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.performance-table {
  margin: 4% 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.table-header {
  display: flex;
  background: #fafafa;
  font-weight: 600;
  font-size: 12px;
  color: #333;
}

.table-row {
  display: flex;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
}

.header-cell,
.table-cell {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
}

.period-cell {
  color: #666;
}

.table-cell.positive {
  color: #52c41a;
}

.table-cell.negative {
  color: #ff4d4f;
}

.ranking-cell {
  color: #666;
}

.performance-analysis {
  margin-top: 4%;
  padding: 4%;
  background: #f8f9fa;
  border-radius: 8px;
}
</style>
