<template>
  <div class="analysis-page">
    <van-nav-bar
      title="我的持仓账本诊断"
      left-arrow
      @click-left="onBack"
      right-text="..."
      class="nav-bar"
    />
    
    <div class="page-content">
      <!-- 标签页 -->
      <div class="tabs-section">
        <van-tabs v-model:active="activeTab" class="analysis-tabs">
          <van-tab title="资产配置" name="asset"></van-tab>
          <van-tab title="收益体验" name="return"></van-tab>
          <van-tab title="风险能力" name="risk"></van-tab>
          <van-tab title="基金诊断" name="fund"></van-tab>
        </van-tabs>
      </div>

      <!-- 资产配置页面 -->
      <div v-if="activeTab === 'asset'" class="tab-content">
        <!-- 资产大类分布 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">资产大类分布</h3>
            <div class="toggle-section">
              <span class="toggle-label">持仓穿透</span>
              <van-switch v-model="holdingPenetration" size="20px" />
            </div>
          </div>
          
          <!-- 饼图区域 -->
          <div class="chart-container">
            <div class="pie-chart">
              <canvas ref="pieCanvas" width="200" height="200"></canvas>
            </div>
            <div class="chart-legend">
              <div class="legend-item" v-for="item in assetDistribution" :key="item.name">
                <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
                <span class="legend-name">{{ item.name }}</span>
                <span class="legend-value">{{ item.percentage }}</span>
              </div>
            </div>
          </div>
          
          <!-- 分析文本 -->
          <div class="analysis-text">
            <p>股票类资产占比过高</p>
            <p>当前股票类资产占比高达60%，债券资产占比仅为10%，希望您的风险承受能力等级较高，当前风险类资产配置偏高，可能会带来超出您的承受能力的风险。</p>
          </div>
        </div>

        <!-- 行业分布 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">行业分布</h3>
            <div class="view-toggle">
              <span class="toggle-text">顶风险</span>
              <van-icon name="arrow-up" />
            </div>
          </div>
          
          <div class="industry-subtitle">
            <span>前五最新报告期</span>
          </div>
          
          <!-- 行业分布图表 -->
          <div class="industry-chart">
            <div class="industry-bars">
              <div class="industry-bar" v-for="industry in industryData" :key="industry.name">
                <div class="bar-container">
                  <div class="bar-fill" :style="{ 
                    width: industry.percentage, 
                    backgroundColor: industry.color 
                  }"></div>
                  <div class="bar-info">
                    <span class="industry-name">{{ industry.name }}</span>
                    <span class="industry-percentage">{{ industry.percentage }}</span>
                  </div>
                </div>
                <div class="industry-change" :class="industry.changeClass">
                  {{ industry.change }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- 行业分析 -->
          <div class="industry-analysis">
            <div class="analysis-highlight">
              <span class="highlight-text">交通运输</span>
              <span class="analysis-desc">行业股本里的以下基金持有</span>
            </div>
            
            <div class="fund-list">
              <div class="fund-item">
                <span class="fund-name">易方达蓝筹精选混合</span>
                <div class="fund-progress">
                  <div class="progress-bar">
                    <div class="progress-fill" style="width: 62%"></div>
                  </div>
                  <span class="fund-percentage">62.46%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 收益表现页面 -->
      <div v-if="activeTab === 'return'" class="tab-content">
        <!-- 回测收益表现 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">回测收益表现</h3>
            <div class="view-toggle">
              <span class="toggle-text">1 顶风险</span>
              <van-icon name="arrow-up" />
            </div>
          </div>

          <!-- 客户期望年化收益 -->
          <div class="expected-return">
            <span class="expected-label">客户期望年化收益</span>
            <span class="expected-value">12%</span>
          </div>

          <!-- 收益指标 -->
          <div class="return-indicators">
            <div class="indicator-item">
              <div class="indicator-dot" style="background: #ff4d4f;"></div>
              <span class="indicator-label">本组合</span>
              <span class="indicator-value positive">3.44%</span>
            </div>
            <div class="indicator-item">
              <div class="indicator-dot" style="background: #1890ff;"></div>
              <span class="indicator-label">沪深300</span>
              <span class="indicator-value positive">3.33%</span>
            </div>
            <div class="indicator-item">
              <div class="indicator-dot" style="background: #faad14;"></div>
              <span class="indicator-label">中证全债</span>
              <span class="indicator-value positive">1.33%</span>
            </div>
          </div>

          <div class="return-period">
            <span class="period-label">业绩表现</span>
            <span class="period-value">12.77%</span>
            <span class="period-label">最大回撤</span>
            <span class="period-value">12.77%</span>
          </div>

          <!-- 收益曲线图 -->
          <div class="chart-container">
            <canvas ref="returnChart" width="350" height="200" class="return-chart"></canvas>
          </div>

          <!-- 时间选择器 -->
          <div class="time-selector">
            <div class="time-tabs">
              <span
                v-for="period in timePeriods"
                :key="period.value"
                :class="['time-tab', { active: selectedPeriod === period.value }]"
                @click="selectPeriod(period.value)"
              >
                {{ period.label }}
              </span>
            </div>
          </div>

          <!-- 历史阶段收益率表现及达标期 -->
          <div class="historical-analysis">
            <h4 class="analysis-title">历史阶段收益率表现及达标期</h4>
            <p class="analysis-desc">
              根据历史数据分析，假设客户在1年的持有期组合基金等
              权重，达不到客户的期望年化收益率3.44%的年化收
              益率，达不到客户的期望年化收益目标。
            </p>
          </div>
        </div>

        <!-- 历史盈利概率 -->
        <div class="section-card">
          <div class="section-header">
            <h3 class="section-title">历史盈利概率</h3>
            <div class="view-toggle">
              <span class="toggle-text">1 顶风险</span>
              <van-icon name="arrow-up" />
            </div>
          </div>

          <p class="probability-desc">
            该组合近5年，在任意时点买入并持有不同时间下的盈利概率
          </p>

          <!-- 概率指标 -->
          <div class="probability-indicators">
            <div class="prob-indicator">
              <div class="prob-dot" style="background: #1890ff;"></div>
              <span class="prob-label">盈利概率 %</span>
            </div>
            <div class="prob-indicator">
              <div class="prob-dot" style="background: #faad14;"></div>
              <span class="prob-label">平均收益率 %</span>
            </div>
          </div>
        </div>
      </div>

      <div v-if="activeTab === 'risk'" class="tab-content">
        <div class="section-card">
          <h3>风险能力分析</h3>
          <p>风险能力相关内容开发中...</p>
        </div>
      </div>

      <div v-if="activeTab === 'fund'" class="tab-content">
        <div class="section-card">
          <h3>基金诊断分析</h3>
          <p>基金诊断相关内容开发中...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 当前活跃标签
const activeTab = ref('asset')

// 持仓穿透开关
const holdingPenetration = ref(true)

// 饼图画布引用
const pieCanvas = ref(null)

// 收益图表画布引用
const returnChart = ref(null)

// 选中的时间周期
const selectedPeriod = ref('近2年')

// 时间周期选项
const timePeriods = ref([
  { label: '近6月', value: '近6月' },
  { label: '近1年', value: '近1年' },
  { label: '近2年', value: '近2年' },
  { label: '近3年', value: '近3年' },
  { label: '近5年', value: '近5年' }
])

// 资产分布数据
const assetDistribution = ref([
  { name: '股票', percentage: '60.00%', color: '#52c41a' },
  { name: '债券', percentage: '30.00%', color: '#1890ff' },
  { name: '商品', percentage: '7.00%', color: '#faad14' },
  { name: '其他', percentage: '3.00%', color: '#d9d9d9' }
])

// 行业分布数据
const industryData = ref([
  { 
    name: '交通运输', 
    percentage: '52.16%', 
    color: '#ff7875',
    change: '近1月 +0.94%',
    changeClass: 'positive'
  },
  { 
    name: '机械设备', 
    percentage: '21.97%', 
    color: '#73d13d',
    change: '近1月 +4.24%',
    changeClass: 'positive'
  }
])

// 绘制饼图
const drawPieChart = () => {
  if (!pieCanvas.value) return

  const canvas = pieCanvas.value
  const ctx = canvas.getContext('2d')
  const centerX = canvas.width / 2
  const centerY = canvas.height / 2
  const radius = 80

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  let currentAngle = -Math.PI / 2 // 从顶部开始

  assetDistribution.value.forEach(item => {
    const percentage = parseFloat(item.percentage) / 100
    const sliceAngle = percentage * 2 * Math.PI

    // 绘制扇形
    ctx.beginPath()
    ctx.moveTo(centerX, centerY)
    ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
    ctx.closePath()
    ctx.fillStyle = item.color
    ctx.fill()

    // 绘制边框
    ctx.strokeStyle = '#fff'
    ctx.lineWidth = 2
    ctx.stroke()

    currentAngle += sliceAngle
  })

  // 绘制中心圆
  ctx.beginPath()
  ctx.arc(centerX, centerY, 25, 0, 2 * Math.PI)
  ctx.fillStyle = '#fff'
  ctx.fill()
}

// 绘制收益曲线图
const drawReturnChart = () => {
  if (!returnChart.value) return

  const canvas = returnChart.value
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 模拟数据点
  const dataPoints = [
    // 本组合数据 (红色)
    { x: 50, y: 120, color: '#ff4d4f' },
    { x: 80, y: 100, color: '#ff4d4f' },
    { x: 110, y: 80, color: '#ff4d4f' },
    { x: 140, y: 90, color: '#ff4d4f' },
    { x: 170, y: 70, color: '#ff4d4f' },
    { x: 200, y: 85, color: '#ff4d4f' },
    { x: 230, y: 75, color: '#ff4d4f' },
    { x: 260, y: 80, color: '#ff4d4f' },
    { x: 290, y: 85, color: '#ff4d4f' },
    { x: 320, y: 90, color: '#ff4d4f' }
  ]

  const dataPoints2 = [
    // 沪深300数据 (蓝色)
    { x: 50, y: 130, color: '#1890ff' },
    { x: 80, y: 110, color: '#1890ff' },
    { x: 110, y: 140, color: '#1890ff' },
    { x: 140, y: 120, color: '#1890ff' },
    { x: 170, y: 160, color: '#1890ff' },
    { x: 200, y: 150, color: '#1890ff' },
    { x: 230, y: 170, color: '#1890ff' },
    { x: 260, y: 160, color: '#1890ff' },
    { x: 290, y: 165, color: '#1890ff' },
    { x: 320, y: 170, color: '#1890ff' }
  ]

  const dataPoints3 = [
    // 中证全债数据 (黄色)
    { x: 50, y: 110, color: '#faad14' },
    { x: 80, y: 105, color: '#faad14' },
    { x: 110, y: 100, color: '#faad14' },
    { x: 140, y: 95, color: '#faad14' },
    { x: 170, y: 90, color: '#faad14' },
    { x: 200, y: 85, color: '#faad14' },
    { x: 230, y: 80, color: '#faad14' },
    { x: 260, y: 75, color: '#faad14' },
    { x: 290, y: 70, color: '#faad14' },
    { x: 320, y: 65, color: '#faad14' }
  ]

  // 绘制网格线
  ctx.strokeStyle = '#f0f0f0'
  ctx.lineWidth = 1

  // 水平网格线
  for (let i = 0; i <= 4; i++) {
    const y = (height / 4) * i
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()
  }

  // 绘制曲线的函数
  const drawCurve = (points, color) => {
    if (points.length < 2) return

    ctx.strokeStyle = color
    ctx.lineWidth = 2
    ctx.beginPath()

    ctx.moveTo(points[0].x, points[0].y)

    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].x, points[i].y)
    }

    ctx.stroke()
  }

  // 绘制三条曲线
  drawCurve(dataPoints, '#ff4d4f')   // 本组合
  drawCurve(dataPoints2, '#1890ff')  // 沪深300
  drawCurve(dataPoints3, '#faad14')  // 中证全债
}

// 选择时间周期
const selectPeriod = (period) => {
  selectedPeriod.value = period
  // 重新绘制图表
  nextTick(() => {
    drawReturnChart()
  })
}

// 返回上一页
const onBack = () => {
  router.back()
}

// 页面挂载后绘制图表
onMounted(() => {
  nextTick(() => {
    drawPieChart()
    drawReturnChart()
  })
})
</script>

<style scoped>
.analysis-page {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.nav-bar {
  background: #ff6b6b !important;
  color: #fff;
  flex-shrink: 0;
}

.page-content {
  flex: 1;
  overflow-y: auto;
}

.tabs-section {
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.analysis-tabs {
  background: #fff;
}

.tab-content {
  padding: 4%;
}

.section-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 4%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4%;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.toggle-section {
  display: flex;
  align-items: center;
  gap: 2%;
}

.toggle-label {
  font-size: 14px;
  color: #666;
}

.view-toggle {
  display: flex;
  align-items: center;
  gap: 1%;
  color: #666;
  font-size: 14px;
}

.chart-container {
  display: flex;
  align-items: center;
  gap: 6%;
  margin-bottom: 4%;
}

.pie-chart {
  flex-shrink: 0;
}

.chart-legend {
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 3%;
  font-size: 14px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 3%;
}

.legend-name {
  flex: 1;
  color: #333;
}

.legend-value {
  color: #666;
  font-weight: 500;
}

.analysis-text {
  padding: 4%;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #ff6b6b;
}

.analysis-text p {
  margin: 0 0 2% 0;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

.analysis-text p:first-child {
  font-weight: 600;
  color: #ff6b6b;
}

.industry-subtitle {
  font-size: 12px;
  color: #999;
  margin-bottom: 4%;
}

.industry-chart {
  margin-bottom: 4%;
}

.industry-bar {
  display: flex;
  align-items: center;
  margin-bottom: 4%;
}

.bar-container {
  flex: 1;
  position: relative;
  height: 40px;
  background: #f5f5f5;
  border-radius: 6px;
  margin-right: 3%;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 6px;
  transition: width 0.3s ease;
}

.bar-info {
  position: absolute;
  top: 50%;
  left: 3%;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  width: 94%;
  font-size: 12px;
}

.industry-name {
  color: #333;
  font-weight: 500;
}

.industry-percentage {
  color: #333;
  font-weight: 600;
}

.industry-change {
  font-size: 12px;
  white-space: nowrap;
}

.industry-change.positive {
  color: #52c41a;
}

.industry-change.negative {
  color: #ff4d4f;
}

.industry-analysis {
  padding: 4%;
  background: #f8f9fa;
  border-radius: 8px;
}

.analysis-highlight {
  margin-bottom: 3%;
  font-size: 14px;
}

.highlight-text {
  color: #ff6b6b;
  font-weight: 600;
}

.analysis-desc {
  color: #666;
}

.fund-list {
  margin-top: 3%;
}

.fund-item {
  display: flex;
  align-items: center;
  gap: 3%;
}

.fund-name {
  font-size: 12px;
  color: #333;
  white-space: nowrap;
}

.fund-progress {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 2%;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #333;
  border-radius: 3px;
}

.fund-percentage {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__text) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}

:deep(.van-tabs__nav) {
  background: #fff;
}

:deep(.van-tab) {
  color: #666;
  font-size: 14px;
}

:deep(.van-tab--active) {
  color: #ff6b6b;
  font-weight: 500;
}

:deep(.van-tabs__line) {
  background: #ff6b6b;
}

/* 收益表现页面样式 */
.expected-return {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3% 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 4%;
}

.expected-label {
  font-size: 14px;
  color: #333;
}

.expected-value {
  font-size: 18px;
  font-weight: 600;
  color: #ff6b6b;
}

.return-indicators {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3%;
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: 2%;
  flex: 1;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.indicator-label {
  font-size: 12px;
  color: #666;
}

.indicator-value {
  font-size: 12px;
  font-weight: 500;
}

.indicator-value.positive {
  color: #52c41a;
}

.indicator-value.negative {
  color: #ff4d4f;
}

.return-period {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4%;
  font-size: 12px;
}

.period-label {
  color: #666;
}

.period-value {
  color: #333;
  font-weight: 500;
}

.return-chart {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.time-selector {
  margin: 4% 0;
}

.time-tabs {
  display: flex;
  justify-content: space-between;
  background: #f5f5f5;
  border-radius: 6px;
  padding: 1%;
}

.time-tab {
  flex: 1;
  text-align: center;
  padding: 2% 0;
  font-size: 12px;
  color: #666;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-tab.active {
  background: #fff;
  color: #ff6b6b;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.historical-analysis {
  margin-top: 4%;
  padding: 4%;
  background: #f8f9fa;
  border-radius: 8px;
}

.analysis-title {
  font-size: 14px;
  color: #333;
  font-weight: 600;
  margin: 0 0 3% 0;
}

.analysis-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.probability-desc {
  font-size: 12px;
  color: #666;
  margin: 0 0 4% 0;
  line-height: 1.4;
}

.probability-indicators {
  display: flex;
  gap: 6%;
}

.prob-indicator {
  display: flex;
  align-items: center;
  gap: 2%;
}

.prob-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.prob-label {
  font-size: 12px;
  color: #666;
}
</style>
