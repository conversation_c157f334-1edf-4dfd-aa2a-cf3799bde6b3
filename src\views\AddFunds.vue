<template>
  <div class="add-funds">
    <van-nav-bar
      title="添加基金"
      left-arrow
      @click-left="$router.back()"
      class="custom-nav-bar"
    >
      <template #right>
        <van-icon name="ellipsis" />
      </template>
    </van-nav-bar>
    
    <!-- 标签页 -->
    <div class="tabs-container">
      <van-tabs v-model:active="activeTab" class="fund-tabs">
        <van-tab title="主动权益" name="active"></van-tab>
        <van-tab title="港股" name="hk"></van-tab>
        <van-tab title="固收+" name="fixed"></van-tab>
        <van-tab title="纯债" name="bond"></van-tab>
        <van-tab title="更多" name="more"></van-tab>
      </van-tabs>
    </div>
    
    <div class="funds-content">
      <!-- 主动权益基金 -->
      <div v-if="activeTab === 'active'" class="fund-list">
        <div class="fund-category">
          <h3 class="category-title">行业均衡</h3>
          
          <!-- 基金项目 -->
          <div 
            v-for="fund in activeFunds" 
            :key="fund.code"
            class="fund-item"
            @click="toggleFund(fund)"
          >
            <div class="fund-header">
              <div class="fund-basic-info">
                <h4 class="fund-name">{{ fund.name }}</h4>
                <p class="fund-code">{{ fund.code }}</p>
                <p class="fund-manager">{{ fund.manager }}</p>
              </div>
              <div class="fund-action">
                <van-icon 
                  :name="fund.selected ? 'minus' : 'plus'" 
                  :class="['action-icon', { selected: fund.selected }]"
                />
              </div>
            </div>
            
            <div class="fund-performance">
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance1Y }}%</span>
                <span class="performance-label">近1年基金收益</span>
              </div>
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance3Y }}%</span>
                <span class="performance-label">近3年基金收益</span>
              </div>
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance5Y }}%</span>
                <span class="performance-label">近5年基金收益</span>
              </div>
            </div>
            
            <div class="fund-description">
              <span class="desc-label">[简介]</span>
              <p class="desc-text">{{ fund.description }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 港股基金 -->
      <div v-if="activeTab === 'hk'" class="fund-list">
        <div class="fund-category">
          <h3 class="category-title">港股基金</h3>
          
          <div 
            v-for="fund in hkFunds" 
            :key="fund.code"
            class="fund-item"
            @click="toggleFund(fund)"
          >
            <div class="fund-header">
              <div class="fund-basic-info">
                <h4 class="fund-name">{{ fund.name }}</h4>
                <p class="fund-code">{{ fund.code }}</p>
                <p class="fund-manager">{{ fund.manager }}</p>
              </div>
              <div class="fund-action">
                <van-icon 
                  :name="fund.selected ? 'minus' : 'plus'" 
                  :class="['action-icon', { selected: fund.selected }]"
                />
              </div>
            </div>
            
            <div class="fund-performance">
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance1Y }}%</span>
                <span class="performance-label">近1年基金收益</span>
              </div>
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance3Y }}%</span>
                <span class="performance-label">近3年基金收益</span>
              </div>
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance5Y }}%</span>
                <span class="performance-label">近5年基金收益</span>
              </div>
            </div>
            
            <div class="fund-description">
              <span class="desc-label">[简介]</span>
              <p class="desc-text">{{ fund.description }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 固收+基金 -->
      <div v-if="activeTab === 'fixed'" class="fund-list">
        <div class="fund-category">
          <h3 class="category-title">固收+基金</h3>
          
          <div 
            v-for="fund in fixedFunds" 
            :key="fund.code"
            class="fund-item"
            @click="toggleFund(fund)"
          >
            <div class="fund-header">
              <div class="fund-basic-info">
                <h4 class="fund-name">{{ fund.name }}</h4>
                <p class="fund-code">{{ fund.code }}</p>
                <p class="fund-manager">{{ fund.manager }}</p>
              </div>
              <div class="fund-action">
                <van-icon 
                  :name="fund.selected ? 'minus' : 'plus'" 
                  :class="['action-icon', { selected: fund.selected }]"
                />
              </div>
            </div>
            
            <div class="fund-performance">
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance1Y }}%</span>
                <span class="performance-label">近1年基金收益</span>
              </div>
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance3Y }}%</span>
                <span class="performance-label">近3年基金收益</span>
              </div>
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance5Y }}%</span>
                <span class="performance-label">近5年基金收益</span>
              </div>
            </div>
            
            <div class="fund-description">
              <span class="desc-label">[简介]</span>
              <p class="desc-text">{{ fund.description }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 纯债基金 -->
      <div v-if="activeTab === 'bond'" class="fund-list">
        <div class="fund-category">
          <h3 class="category-title">纯债基金</h3>
          
          <div 
            v-for="fund in bondFunds" 
            :key="fund.code"
            class="fund-item"
            @click="toggleFund(fund)"
          >
            <div class="fund-header">
              <div class="fund-basic-info">
                <h4 class="fund-name">{{ fund.name }}</h4>
                <p class="fund-code">{{ fund.code }}</p>
                <p class="fund-manager">{{ fund.manager }}</p>
              </div>
              <div class="fund-action">
                <van-icon 
                  :name="fund.selected ? 'minus' : 'plus'" 
                  :class="['action-icon', { selected: fund.selected }]"
                />
              </div>
            </div>
            
            <div class="fund-performance">
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance1Y }}%</span>
                <span class="performance-label">近1年基金收益</span>
              </div>
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance3Y }}%</span>
                <span class="performance-label">近3年基金收益</span>
              </div>
              <div class="performance-item">
                <span class="performance-value">{{ fund.performance5Y }}%</span>
                <span class="performance-label">近5年基金收益</span>
              </div>
            </div>
            
            <div class="fund-description">
              <span class="desc-label">[简介]</span>
              <p class="desc-text">{{ fund.description }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 更多基金 -->
      <div v-if="activeTab === 'more'" class="fund-list">
        <div class="fund-category">
          <h3 class="category-title">更多基金</h3>
          <div class="empty-state">
            <van-icon name="search" class="empty-icon" />
            <p class="empty-text">更多基金类型敬请期待</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部确认按钮 -->
    <div class="bottom-actions">
      <van-button 
        type="primary" 
        block 
        round
        class="confirm-btn"
        @click="confirmSelection"
        :disabled="selectedFunds.length === 0"
      >
        确认添加 ({{ selectedFunds.length }})
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 当前活跃标签
const activeTab = ref('active')

// 主动权益基金数据
const activeFunds = ref([
  {
    code: 'A000625',
    name: '大成高新技术产业A',
    manager: '基金经理：韩创',
    performance1Y: '80.22',
    performance3Y: '50.22',
    performance5Y: '50.22',
    description: '该基金专注于高新技术产业投资，注重企业创新能力。',
    selected: false
  },
  {
    code: 'A000826',
    name: '广发中证全指电力公用事业ETF',
    manager: '基金经理：刘杰',
    performance1Y: '12.44',
    performance3Y: '15.32',
    performance5Y: '18.76',
    description: '跟踪中证全指电力公用事业指数，分散投资电力行业。',
    selected: false
  },
  {
    code: 'A001156',
    name: '申万菱信中证军工指数',
    manager: '基金经理：袁英杰',
    performance1Y: '25.18',
    performance3Y: '32.45',
    performance5Y: '28.91',
    description: '专注军工行业投资，把握国防建设投资机会。',
    selected: false
  }
])

// 港股基金数据
const hkFunds = ref([
  {
    code: 'H000826',
    name: '南方香港优选股票',
    manager: '基金经理：张华',
    performance1Y: '75.18',
    performance3Y: '45.32',
    performance5Y: '48.76',
    description: '专注投资香港市场优质股票，把握港股投资机会。',
    selected: false
  },
  {
    code: 'H164906',
    name: '交银中证海外中国互联网指数',
    manager: '基金经理：蔡铮',
    performance1Y: '-15.22',
    performance3Y: '8.45',
    performance5Y: '12.33',
    description: '跟踪海外中国互联网指数，投资中概股龙头企业。',
    selected: false
  }
])

// 固收+基金数据
const fixedFunds = ref([
  {
    code: 'F163322',
    name: '中银丰禧债券',
    manager: '基金经理：李明',
    performance1Y: '8.22',
    performance3Y: '6.85',
    performance5Y: '7.12',
    description: '该基金采用固收+策略，在控制风险的基础上追求稳健收益。',
    selected: false
  },
  {
    code: 'F000251',
    name: '工银金融地产混合A',
    manager: '基金经理：王君正',
    performance1Y: '12.45',
    performance3Y: '9.32',
    performance5Y: '8.76',
    description: '专注金融地产行业投资，采用价值投资策略。',
    selected: false
  }
])

// 纯债基金数据
const bondFunds = ref([
  {
    code: 'B110007',
    name: '易方达稳健收益债券A',
    manager: '基金经理：王强',
    performance1Y: '6.22',
    performance3Y: '4.85',
    performance5Y: '5.12',
    description: '专注投资债券市场，追求稳定收益，风险较低。',
    selected: false
  },
  {
    code: 'B000015',
    name: '华夏纯债债券A',
    manager: '基金经理：刘芳',
    performance1Y: '5.88',
    performance3Y: '4.32',
    performance5Y: '4.76',
    description: '纯债券投资策略，适合稳健型投资者配置。',
    selected: false
  }
])

// 计算已选择的基金
const selectedFunds = computed(() => {
  const allFunds = [
    ...activeFunds.value,
    ...hkFunds.value,
    ...fixedFunds.value,
    ...bondFunds.value
  ]
  return allFunds.filter(fund => fund.selected)
})

// 切换基金选择状态
const toggleFund = (fund) => {
  fund.selected = !fund.selected
  showToast(fund.selected ? '已添加基金' : '已移除基金')
}

// 确认选择
const confirmSelection = () => {
  if (selectedFunds.value.length === 0) {
    showToast('请至少选择一只基金')
    return
  }
  
  showToast(`已添加 ${selectedFunds.value.length} 只基金`)
  
  // 这里可以将选择的基金数据传递回上一页面
  // 或者保存到全局状态管理中
  
  setTimeout(() => {
    router.back()
  }, 1000)
}
</script>

<style scoped>
.add-funds {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.custom-nav-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar .van-icon) {
  color: #fff;
}

/* 标签页样式 */
.tabs-container {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.fund-tabs {
  background: #fff;
}

:deep(.van-tabs__nav) {
  background: #fff;
}

:deep(.van-tab) {
  font-size: 14px;
  color: #666;
}

:deep(.van-tab--active) {
  color: #667eea;
  font-weight: 600;
}

:deep(.van-tabs__line) {
  background: #667eea;
}

/* 基金列表样式 */
.funds-content {
  flex: 1;
  overflow-y: auto;
  padding: 4%;
  padding-bottom: 100px;
}

.fund-category {
  margin-bottom: 4%;
}

.category-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4% 0;
  padding-left: 2%;
}

.fund-item {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 3%;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.fund-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.fund-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 3%;
}

.fund-basic-info {
  flex: 1;
}

.fund-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 1% 0;
}

.fund-code {
  font-size: 12px;
  color: #999;
  margin: 0 0 1% 0;
}

.fund-manager {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.fund-action {
  margin-left: 3%;
}

.action-icon {
  font-size: 20px;
  color: #1890ff;
  padding: 2%;
  border-radius: 50%;
  background: #f0f8ff;
  transition: all 0.3s ease;
}

.action-icon.selected {
  color: #fff;
  background: #1890ff;
}

/* 基金业绩样式 */
.fund-performance {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3%;
  padding: 3% 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.performance-item {
  text-align: center;
  flex: 1;
}

.performance-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #52c41a;
  margin-bottom: 1%;
}

.performance-label {
  font-size: 10px;
  color: #999;
}

/* 基金描述样式 */
.fund-description {
  margin-top: 2%;
}

.desc-label {
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.desc-text {
  font-size: 11px;
  color: #666;
  line-height: 1.5;
  margin: 1% 0 0 0;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 10% 0;
}

.empty-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 4%;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin: 0;
}

/* 底部按钮样式 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 4%;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  z-index: 1000;
}

.confirm-btn {
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
}

.confirm-btn:disabled {
  background: #d9d9d9;
  color: #999;
}
</style>
