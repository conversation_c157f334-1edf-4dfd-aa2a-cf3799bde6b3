import { createRouter, createWebHistory } from 'vue-router'
import AccountList from '../views/AccountList.vue'
import AccountDetail from '../views/AccountDetail.vue'
import AccountDiagnosis from '../views/AccountDiagnosis.vue'
import SelectAccountType from '../views/SelectAccountType.vue'
import CreateHoldingAccount from '../views/CreateHoldingAccount.vue'
import SelectHoldingProducts from '../views/SelectHoldingProducts.vue'
import AccountAnalysis from '../views/AccountAnalysis.vue'
import ImportExternalHoldings from '../views/ImportExternalHoldings.vue'
import CreateWishAccount from '../views/CreateWishAccount.vue'
import GenerateReport from '../views/GenerateReport.vue'
import AddFunds from '../views/AddFunds.vue'

const routes = [
  {
    path: '/',
    name: 'AccountList',
    component: AccountList,
    meta: {
      title: '账本列表'
    }
  },
  {
    path: '/account/:id',
    name: 'AccountDetail',
    component: AccountDetail,
    meta: {
      title: '我的投资账本'
    }
  },
  {
    path: '/diagnosis',
    name: 'AccountDiagnosis',
    component: AccountDiagnosis,
    meta: {
      title: '我的投资账本'
    }
  },
  {
    path: '/select-type',
    name: 'SelectAccountType',
    component: SelectAccountType,
    meta: {
      title: '选择账本类型'
    }
  },
  {
    path: '/create-holding',
    name: 'CreateHoldingAccount',
    component: CreateHoldingAccount,
    meta: {
      title: '创建持仓账本'
    }
  },
  {
    path: '/select-products',
    name: 'SelectHoldingProducts',
    component: SelectHoldingProducts,
    meta: {
      title: '选择持仓产品'
    }
  },
  {
    path: '/account-analysis/:id',
    name: 'AccountAnalysis',
    component: AccountAnalysis,
    meta: {
      title: '我的持仓账本诊断'
    }
  },
  {
    path: '/import-holdings',
    name: 'ImportExternalHoldings',
    component: ImportExternalHoldings,
    meta: {
      title: '导入外部持仓'
    }
  },
  {
    path: '/create-wish',
    name: 'CreateWishAccount',
    component: CreateWishAccount,
    meta: {
      title: '创建王维的心愿账本'
    }
  },
  {
    path: '/generate-report',
    name: 'GenerateReport',
    component: GenerateReport,
    meta: {
      title: '生成资配报告'
    }
  },
  {
    path: '/add-funds',
    name: 'AddFunds',
    component: AddFunds,
    meta: {
      title: '添加基金'
    }
  },

]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫，设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

export default router
