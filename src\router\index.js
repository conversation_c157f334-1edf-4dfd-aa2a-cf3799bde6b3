import { createRouter, createWebHistory } from 'vue-router'
import AccountList from '../views/AccountList.vue'
import AccountDetail from '../views/AccountDetail.vue'
import AccountDiagnosis from '../views/AccountDiagnosis.vue'

const routes = [
  {
    path: '/',
    name: 'AccountList',
    component: AccountList,
    meta: {
      title: '账本列表'
    }
  },
  {
    path: '/account/:id',
    name: 'AccountDetail',
    component: AccountDetail,
    meta: {
      title: '我的投资账本'
    }
  },
  {
    path: '/diagnosis',
    name: 'AccountDiagnosis',
    component: AccountDiagnosis,
    meta: {
      title: '我的投资账本'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫，设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

export default router
