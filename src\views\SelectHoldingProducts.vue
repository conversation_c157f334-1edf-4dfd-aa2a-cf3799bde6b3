<template>
  <div class="select-products-page">
    <van-nav-bar
      title="创建持仓账本"
      left-arrow
      @click-left="onBack"
      right-text="..."
      class="nav-bar"
    />
    
    <div class="page-content">
      <!-- 步骤进度条 -->
      <div class="progress-section">
        <van-steps :active="currentStep" active-color="#ff6b6b" inactive-color="#e5e5e5">
          <van-step>设置投资目标</van-step>
          <van-step>选择持仓产品</van-step>
          <van-step>完成</van-step>
        </van-steps>
      </div>
      
      <!-- 数据日期 -->
      <div class="data-info">
        <span class="data-date">数据日期：{{ dataDate }}</span>
        <p class="data-note">
          注：以下基金在本机构所有公募基金持仓产品，请选择你持有合适基金进行导入。
        </p>
      </div>
      
      <!-- 持仓基金列表 -->
      <div class="funds-section">
        <div class="section-header" @click="toggleHoldingFunds">
          <h3 class="section-title">持仓基金</h3>
          <van-icon :name="showHoldingFunds ? 'arrow-up' : 'arrow-down'" />
        </div>
        
        <div v-show="showHoldingFunds" class="funds-list">
          <div 
            v-for="fund in holdingFunds" 
            :key="fund.id"
            class="fund-item"
            :class="{ 'selected': selectedFunds.includes(fund.id) }"
            @click="toggleFundSelection(fund.id)"
          >
            <div class="fund-checkbox">
              <van-icon 
                v-if="selectedFunds.includes(fund.id)" 
                name="checked" 
                color="#ff6b6b" 
              />
              <div v-else class="checkbox-empty"></div>
            </div>
            <div class="fund-info">
              <div class="fund-name">{{ fund.name }}</div>
              <div class="fund-details">
                <span class="fund-code">{{ fund.code }}</span>
                <span class="fund-amount">{{ fund.amount }}</span>
                <span class="fund-value">{{ fund.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 导入基金 -->
      <div class="import-section">
        <div class="section-header" @click="toggleImportFunds">
          <h3 class="section-title">导入基金</h3>
          <van-icon :name="showImportFunds ? 'arrow-up' : 'arrow-down'" />
        </div>
        
        <div v-show="showImportFunds" class="import-content">
          <van-button 
            block 
            plain 
            class="import-btn"
            @click="goToImportPage"
          >
            + 添加导入基金
          </van-button>
          
          <!-- 已导入的基金列表 -->
          <div v-if="importedFunds.length > 0" class="imported-funds">
            <div
              v-for="fund in importedFunds"
              :key="fund.id"
              class="fund-item imported"
              :class="{ 'selected': selectedImportedFunds.includes(fund.id) }"
              @click="toggleImportedFundSelection(fund.id)"
            >
              <div class="fund-checkbox">
                <van-icon
                  v-if="selectedImportedFunds.includes(fund.id)"
                  name="checked"
                  color="#ff6b6b"
                />
                <div v-else class="checkbox-empty"></div>
              </div>
              <div class="fund-info">
                <div class="fund-name">{{ fund.name }}</div>
                <div class="fund-details">
                  <span class="fund-code">{{ fund.code }}</span>
                  <span class="fund-amount">{{ fund.holdingAmount || fund.amount }}</span>
                </div>
              </div>
              <van-icon
                name="cross"
                class="remove-btn"
                @click.stop="removeImportedFund(fund.id)"
              />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部统计和按钮 -->
      <div class="bottom-section">
        <div class="selection-summary">
          <div class="summary-item" @click="toggleSelectAll">
            <van-icon
              :name="isAllSelected ? 'checked' : 'circle'"
              :color="isAllSelected ? '#ff6b6b' : '#ddd'"
            />
            <span class="summary-text">全选</span>
          </div>
          <div class="summary-total">
            基金总数：{{ totalSelectedCount }}
          </div>
        </div>
        
        <div class="bottom-actions">
          <van-button 
            class="back-btn"
            @click="onBack"
          >
            上一步
          </van-button>
          <van-button 
            class="complete-btn"
            :disabled="totalSelectedCount === 0"
            @click="onComplete"
          >
            完成创建
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import {
  getFormData,
  updateStepData,
  getStepData,
  submitFormData
} from '../utils/formDataManager.js'
import { getHoldingFunds, createAccount } from '../api/mockApi.js'

const router = useRouter()

// 当前步骤
const currentStep = ref(1)

// 数据日期
const dataDate = ref('2024-06-01')

// 展开/收起状态
const showHoldingFunds = ref(true)
const showImportFunds = ref(true)

// 选中的基金ID列表
const selectedFunds = ref([])

// 导入的基金列表
const importedFunds = ref([])

// 选中的导入基金ID列表
const selectedImportedFunds = ref([])

// 持仓基金数据
const holdingFunds = ref([])

// 加载状态
const loading = ref(false)

// 计算总选中数量
const totalSelectedCount = computed(() => {
  return selectedFunds.value.length + selectedImportedFunds.value.length
})

// 计算是否全选
const isAllSelected = computed(() => {
  const totalFunds = holdingFunds.value.length + importedFunds.value.length
  const totalSelected = selectedFunds.value.length + selectedImportedFunds.value.length
  return totalFunds > 0 && totalSelected === totalFunds
})

// 全选/取消全选
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    // 取消全选
    selectedFunds.value = []
    selectedImportedFunds.value = []
  } else {
    // 全选
    selectedFunds.value = holdingFunds.value.map(fund => fund.id)
    selectedImportedFunds.value = importedFunds.value.map(fund => fund.id)
  }
  saveSelectedFunds()
}

// 切换基金选择
const toggleFundSelection = (fundId) => {
  const index = selectedFunds.value.indexOf(fundId)
  if (index > -1) {
    selectedFunds.value.splice(index, 1)
  } else {
    selectedFunds.value.push(fundId)
  }
  
  // 保存选择状态
  saveSelectedFunds()
}

// 切换持仓基金展开状态
const toggleHoldingFunds = () => {
  showHoldingFunds.value = !showHoldingFunds.value
}

// 切换导入基金展开状态
const toggleImportFunds = () => {
  showImportFunds.value = !showImportFunds.value
}

// 跳转到导入页面
const goToImportPage = () => {
  router.push({ name: 'ImportExternalHoldings' })
}

// 根据投资期限获取风险目标
const getRiskTargetByPeriod = (period) => {
  const riskTargetMap = {
    '1-3years': ['计划投资 1年-3年', '短期理财', '最大亏损 3%以下'],
    '3-5years': ['计划投资 3年-5年', '稳健增值', '最大亏损 5%以下'],
    '5years+': ['计划投资 5年以上', '长期增值', '最大亏损 10%以下']
  }
  return riskTargetMap[period] || ['计划投资 1年-3年', '短期理财', '最大亏损 3%以下']
}

// 移除导入的基金
const removeImportedFund = (fundId) => {
  const index = importedFunds.value.findIndex(fund => fund.id === fundId)
  if (index > -1) {
    importedFunds.value.splice(index, 1)
    saveSelectedFunds()
  }
}

// 保存选中的基金数据
const saveSelectedFunds = () => {
  const selectedHoldingFunds = holdingFunds.value.filter(fund => 
    selectedFunds.value.includes(fund.id)
  )
  
  const allSelectedFunds = [...selectedHoldingFunds, ...importedFunds.value]
  
  updateStepData(2, { 
    selectedProducts: allSelectedFunds,
    selectedFundIds: selectedFunds.value,
    importedFunds: importedFunds.value
  })
}

// 返回上一步
const onBack = () => {
  router.back()
}

// 完成创建
const onComplete = async () => {
  if (totalSelectedCount.value === 0) {
    showToast('请至少选择一个基金')
    return
  }

  try {
    // 保存最终选择的基金
    saveSelectedFunds()

    // 获取完整的表单数据
    const formData = getFormData()

    // 准备提交数据
    const submitData = {
      account_type: formData.accountType,
      account_name: formData.accountName,
      investment_period: formData.step1.investmentPeriod,
      risk_target: getRiskTargetByPeriod(formData.step1.investmentPeriod),
      selected_products: [...selectedHoldingFunds.value, ...importedFunds.value],
      create_time: new Date().toISOString()
    }

    // 提交到Mock API
    showToast.loading('正在创建账本...')
    const result = await createAccount(submitData)
    showToast.success('账本创建成功！')

    console.log('创建结果：', result)

    // 清除表单数据
    localStorage.removeItem('createAccountFormData')

    // 跳转到账本详情页面
    setTimeout(() => {
      router.push({
        name: 'AccountDetail',
        params: { id: result.data.id }
      })
    }, 1500)

  } catch (error) {
    showToast.fail(error.message || '创建失败，请重试')
    console.error('创建账本失败：', error)
  }
}

// 计算选中的持仓基金
const selectedHoldingFunds = computed(() => {
  return holdingFunds.value.filter(fund =>
    selectedFunds.value.includes(fund.id)
  )
})

// 加载持仓基金数据
const loadHoldingFunds = async () => {
  try {
    loading.value = true
    const response = await getHoldingFunds()
    if (response.success) {
      holdingFunds.value = response.data.funds
      dataDate.value = response.data.dataDate
    }
  } catch (error) {
    showToast.fail('加载基金数据失败')
    console.error('加载基金数据失败：', error)
  } finally {
    loading.value = false
  }
}

// 初始化页面数据
onMounted(async () => {
  // 加载持仓基金数据
  await loadHoldingFunds()

  // 加载已保存的步骤2数据
  const step2Data = getStepData(2)
  if (step2Data.selectedFundIds) {
    selectedFunds.value = step2Data.selectedFundIds
  }
  if (step2Data.importedFunds) {
    importedFunds.value = step2Data.importedFunds
  }
})

// 监听路由变化，当从导入页面返回时重新加载数据
router.afterEach((to, from) => {
  if (from.name === 'ImportExternalHoldings' && to.name === 'SelectHoldingProducts') {
    // 重新加载导入的基金数据
    const step2Data = getStepData(2)
    if (step2Data.importedFunds) {
      importedFunds.value = step2Data.importedFunds
    }
  }
})
</script>

<style scoped>
/* 页面容器 */
.select-products-page {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.nav-bar {
  background: #ff6b6b !important;
  color: #fff;
  flex-shrink: 0;
}

/* 页面内容 */
.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 120px;
  overflow-y: auto;
}

/* 步骤进度条 */
.progress-section {
  background: #fff;
  padding: 5% 4%;
  margin-bottom: 3%;
}

/* 数据信息 */
.data-info {
  background: #fff;
  padding: 4%;
  margin-bottom: 3%;
}

.data-date {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.data-note {
  font-size: 12px;
  color: #666;
  margin: 2% 0 0 0;
  line-height: 1.4;
}

/* 基金区域 */
.funds-section,
.import-section {
  background: #fff;
  margin-bottom: 3%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4%;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  margin: 0;
}

/* 基金列表 */
.funds-list {
  padding: 0 4%;
}

.fund-item {
  display: flex;
  align-items: center;
  padding: 4% 0;
  border-bottom: 1px solid #f8f8f8;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.fund-item:last-child {
  border-bottom: none;
}

.fund-item:active {
  background-color: #f8f8f8;
}

.fund-item.selected {
  background-color: #fff5f5;
}

.fund-checkbox {
  margin-right: 3%;
  flex-shrink: 0;
}

.checkbox-empty {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 50%;
}

.fund-info {
  flex: 1;
}

.fund-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 2%;
}

.fund-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

/* 导入区域 */
.import-content {
  padding: 4%;
}

.import-btn {
  border: 2px dashed #ff6b6b !important;
  color: #ff6b6b !important;
  background: #fff !important;
  height: 44px !important;
  margin-bottom: 4%;
}

.imported-funds .fund-item {
  background-color: #f8f8f8;
  border-radius: 8px;
  margin-bottom: 2%;
  padding: 3%;
}

.remove-btn {
  color: #999;
  font-size: 16px;
  padding: 2%;
}

/* 底部区域 */
.bottom-section {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
}

.selection-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3% 4%;
  border-bottom: 1px solid #f0f0f0;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 2%;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.summary-item:active {
  opacity: 0.7;
}

.summary-text {
  font-size: 14px;
  color: #333;
}

.summary-total {
  font-size: 14px;
  color: #666;
}

.bottom-actions {
  display: flex;
  gap: 3%;
  padding: 4%;
}

.back-btn {
  flex: 1;
  background: #f5f5f5 !important;
  border: none !important;
  color: #666 !important;
  height: 44px !important;
  border-radius: 8px !important;
}

.complete-btn {
  flex: 2;
  background: #ff6b6b !important;
  border: none !important;
  color: #fff !important;
  height: 44px !important;
  border-radius: 8px !important;
}

.complete-btn:disabled {
  background: #ddd !important;
  color: #999 !important;
}

/* 导航栏样式优化 */
:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__text) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}

/* 步骤条样式优化 */
:deep(.van-step__title) {
  font-size: 13px;
  color: #666;
}

:deep(.van-step__title--active) {
  color: #ff6b6b;
  font-weight: 500;
}
</style>
