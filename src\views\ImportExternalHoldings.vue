<template>
  <div class="import-page">
    <van-nav-bar
      title="导入外部持仓"
      left-arrow
      @click-left="onBack"
      right-text="..."
      class="nav-bar"
    />
    
    <div class="page-content">
      <!-- 导入方式选择 -->
      <div class="import-methods">
        <van-button 
          :class="['method-btn', { active: importMethod === 'upload' }]"
          @click="setImportMethod('upload')"
        >
          上传截图
        </van-button>
        <van-button 
          :class="['method-btn', { active: importMethod === 'manual' }]"
          @click="setImportMethod('manual')"
        >
          手动添加
        </van-button>
      </div>
      
      <!-- 上传截图模式 -->
      <div v-if="importMethod === 'upload'" class="upload-section">
        <div class="upload-tip">
          <p class="tip-text">请上传带有基金名称、持仓金额的持仓截图</p>
          <div class="tip-details">
            <span class="tip-date">截图日期：2024年07月03日</span>
            <span class="tip-source">来源：支付宝</span>
          </div>
        </div>
        
        <!-- 基金持仓截图示例 -->
        <div class="holdings-preview">
          <div class="preview-header">
            <div class="preview-tabs">
              <span class="tab active">持仓</span>
              <span class="tab">收益</span>
              <span class="tab">交易</span>
            </div>
          </div>
          
          <!-- 全选控制 -->
          <div class="select-all-section">
            <div class="select-all-item" @click="toggleSelectAllExternal">
              <van-icon
                :name="isAllExternalSelected ? 'checked' : 'circle'"
                :color="isAllExternalSelected ? '#ff6b6b' : '#ddd'"
              />
              <span class="select-all-text">全选</span>
            </div>
            <div class="selected-count">
              已选择 {{ selectedExternalFunds.length }} 个基金
            </div>
          </div>

          <!-- Mock基金列表 -->
          <div class="mock-holdings">
            <div
              v-for="fund in mockExternalFunds"
              :key="fund.id"
              class="holding-item"
              :class="{ 'selected': selectedExternalFunds.includes(fund.id) }"
              @click="toggleExternalFund(fund.id)"
            >
              <div class="fund-checkbox">
                <van-icon
                  v-if="selectedExternalFunds.includes(fund.id)"
                  name="checked"
                  color="#ff6b6b"
                />
                <div v-else class="checkbox-empty"></div>
              </div>
              <div class="holding-info">
                <div class="fund-name">{{ fund.name }}</div>
                <div class="fund-stats">
                  <div class="stat-item">
                    <span class="stat-label">持仓金额</span>
                    <span class="stat-value">{{ fund.holdingAmount }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">单位净值</span>
                    <span class="stat-value">{{ fund.unitValue }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">累计收益</span>
                    <span class="stat-value profit">{{ fund.totalProfit }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 支付宝标识 -->
          <div class="alipay-footer">
            <div class="alipay-logo">
              <div class="logo-icon"></div>
              <span class="logo-text">支付宝E账户持仓截图</span>
            </div>
          </div>
        </div>
        
        <!-- 上传按钮 -->
        <div class="upload-action">
          <van-button 
            block 
            class="upload-btn"
            @click="selectImage"
          >
            选择一张或多张图片
          </van-button>
        </div>
      </div>
      
      <!-- 手动添加模式 -->
      <div v-if="importMethod === 'manual'" class="manual-section">
        <div class="manual-tip">
          <p>手动添加基金信息</p>
        </div>
        
        <!-- 手动添加表单 -->
        <div class="manual-form">
          <van-field
            v-model="manualFund.name"
            label="基金名称"
            placeholder="请输入基金名称"
            required
          />
          <van-field
            v-model="manualFund.code"
            label="基金代码"
            placeholder="请输入基金代码"
            required
          />
          <van-field
            v-model="manualFund.amount"
            label="持仓金额"
            placeholder="请输入持仓金额"
            type="number"
            required
          />
          <van-field
            v-model="manualFund.unitValue"
            label="单位净值"
            placeholder="请输入单位净值"
            type="number"
          />
        </div>
        
        <van-button 
          block 
          class="add-manual-btn"
          @click="addManualFund"
        >
          添加基金
        </van-button>
      </div>
      
      <!-- 底部按钮 -->
      <div class="bottom-actions">
        <van-button 
          class="back-btn"
          @click="onBack"
        >
          返回
        </van-button>
        <van-button 
          class="save-btn"
          :disabled="selectedExternalFunds.length === 0"
          @click="saveImportedFunds"
        >
          保存
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { updateStepData } from '../utils/formDataManager.js'

const router = useRouter()

// 导入方式
const importMethod = ref('upload')

// 选中的外部基金
const selectedExternalFunds = ref([])

// 手动添加的基金信息
const manualFund = reactive({
  name: '',
  code: '',
  amount: '',
  unitValue: ''
})

// Mock外部基金数据
const mockExternalFunds = ref([
  {
    id: 'ext001',
    name: '天弘余额宝货币',
    code: '000198',
    holdingAmount: '7,317.15',
    unitValue: '1.0330',
    totalProfit: '8,708.42'
  },
  {
    id: 'ext002',
    name: '易方达蓝筹精选',
    code: '005827',
    holdingAmount: '2,234.19',
    unitValue: '2.0685',
    totalProfit: '4,621.42'
  },
  {
    id: 'ext003',
    name: '华夏成长混合',
    code: '000001',
    holdingAmount: '1,376.81',
    unitValue: '1.6331',
    totalProfit: '2,063.72'
  },
  {
    id: 'ext004',
    name: '南方积极配置',
    code: '202005',
    holdingAmount: '759.15',
    unitValue: '1.0506',
    totalProfit: '820.34'
  },
  {
    id: 'ext005',
    name: '博时主题行业',
    code: '160505',
    holdingAmount: '341.93',
    unitValue: '1.0768',
    totalProfit: '367.85'
  }
])

// 设置导入方式
const setImportMethod = (method) => {
  importMethod.value = method
}

// 计算是否全选外部基金
const isAllExternalSelected = computed(() => {
  return mockExternalFunds.value.length > 0 &&
         selectedExternalFunds.value.length === mockExternalFunds.value.length
})

// 全选/取消全选外部基金
const toggleSelectAllExternal = () => {
  if (isAllExternalSelected.value) {
    selectedExternalFunds.value = []
  } else {
    selectedExternalFunds.value = mockExternalFunds.value.map(fund => fund.id)
  }
}

// 切换外部基金选择
const toggleExternalFund = (fundId) => {
  const index = selectedExternalFunds.value.indexOf(fundId)
  if (index > -1) {
    selectedExternalFunds.value.splice(index, 1)
  } else {
    selectedExternalFunds.value.push(fundId)
  }
}

// 选择图片
const selectImage = () => {
  showToast('图片上传功能待实现')
}

// 添加手动基金
const addManualFund = () => {
  if (!manualFund.name || !manualFund.code || !manualFund.amount) {
    showToast('请填写完整的基金信息')
    return
  }
  
  const newFund = {
    id: `manual_${Date.now()}`,
    name: manualFund.name,
    code: manualFund.code,
    holdingAmount: manualFund.amount,
    unitValue: manualFund.unitValue || '1.0000',
    totalProfit: '0.00',
    isManual: true
  }
  
  mockExternalFunds.value.push(newFund)
  selectedExternalFunds.value.push(newFund.id)
  
  // 重置表单
  Object.assign(manualFund, {
    name: '',
    code: '',
    amount: '',
    unitValue: ''
  })
  
  showToast('基金添加成功')
}

// 返回
const onBack = () => {
  router.back()
}

// 保存导入的基金
const saveImportedFunds = () => {
  if (selectedExternalFunds.value.length === 0) {
    showToast('请至少选择一个基金')
    return
  }
  
  const selectedFunds = mockExternalFunds.value.filter(fund => 
    selectedExternalFunds.value.includes(fund.id)
  )
  
  // 保存导入的基金到表单数据
  updateStepData(2, { 
    importedFunds: selectedFunds
  })
  
  showToast.success(`已选择 ${selectedFunds.length} 个基金`)
  
  setTimeout(() => {
    router.back()
  }, 1000)
}
</script>

<style scoped>
.import-page {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.nav-bar {
  background: #ff6b6b !important;
  color: #fff;
  flex-shrink: 0;
}

.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-bottom: 80px;
  overflow-y: auto;
}

.import-methods {
  display: flex;
  gap: 3%;
  padding: 4%;
  background: #fff;
  margin-bottom: 3%;
}

.method-btn {
  flex: 1;
  height: 40px !important;
  border: 1px solid #ddd !important;
  background: #fff !important;
  color: #666 !important;
  border-radius: 6px !important;
}

.method-btn.active {
  background: #ff6b6b !important;
  border-color: #ff6b6b !important;
  color: #fff !important;
}

.upload-section {
  flex: 1;
}

.upload-tip {
  background: #fff;
  padding: 4%;
  margin-bottom: 3%;
}

.tip-text {
  font-size: 14px;
  color: #333;
  margin: 0 0 2% 0;
}

.tip-details {
  display: flex;
  gap: 4%;
  font-size: 12px;
  color: #666;
}

.holdings-preview {
  background: #fff;
  margin-bottom: 3%;
}

.preview-header {
  padding: 3% 4%;
  border-bottom: 1px solid #f0f0f0;
}

.preview-tabs {
  display: flex;
  gap: 6%;
}

.tab {
  font-size: 14px;
  color: #666;
  padding: 2% 0;
}

.tab.active {
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
}

.select-all-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3% 4%;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.select-all-item {
  display: flex;
  align-items: center;
  gap: 2%;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.select-all-item:active {
  opacity: 0.7;
}

.select-all-text {
  font-size: 14px;
  color: #333;
}

.selected-count {
  font-size: 12px;
  color: #666;
}

.mock-holdings {
  padding: 0 4%;
}

.holding-item {
  display: flex;
  align-items: center;
  padding: 3% 0;
  border-bottom: 1px solid #f8f8f8;
  cursor: pointer;
}

.holding-item:last-child {
  border-bottom: none;
}

.holding-item.selected {
  background-color: #fff5f5;
}

.fund-checkbox {
  margin-right: 3%;
  flex-shrink: 0;
}

.checkbox-empty {
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 50%;
}

.holding-info {
  flex: 1;
}

.fund-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 2%;
}

.fund-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 11px;
}

.stat-label {
  color: #999;
  margin-bottom: 1%;
}

.stat-value {
  color: #333;
  font-weight: 500;
}

.stat-value.profit {
  color: #f56c6c;
}

.alipay-footer {
  padding: 3% 4%;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.alipay-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2%;
}

.logo-icon {
  width: 16px;
  height: 16px;
  background: #1890ff;
  border-radius: 50%;
}

.logo-text {
  font-size: 12px;
  color: #666;
}

.upload-action {
  padding: 0 4%;
}

.upload-btn {
  background: #1890ff !important;
  border: none !important;
  color: #fff !important;
  height: 44px !important;
  border-radius: 8px !important;
}

.manual-section {
  flex: 1;
  background: #fff;
  margin-bottom: 3%;
}

.manual-tip {
  padding: 4%;
  border-bottom: 1px solid #f0f0f0;
}

.manual-tip p {
  font-size: 14px;
  color: #333;
  margin: 0;
}

.manual-form {
  padding: 4%;
}

.add-manual-btn {
  margin: 0 4% 4% 4%;
  background: #ff6b6b !important;
  border: none !important;
  color: #fff !important;
  height: 44px !important;
  border-radius: 8px !important;
}

.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  gap: 3%;
  padding: 4%;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
}

.back-btn {
  flex: 1;
  background: #f5f5f5 !important;
  border: none !important;
  color: #666 !important;
  height: 44px !important;
  border-radius: 8px !important;
}

.save-btn {
  flex: 1;
  background: #ff6b6b !important;
  border: none !important;
  color: #fff !important;
  height: 44px !important;
  border-radius: 8px !important;
}

.save-btn:disabled {
  background: #ddd !important;
  color: #999 !important;
}

:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__text) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}
</style>
