// 心愿账本相关API

// 模拟API延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 提交投资目标数据，获取方案建议
export const submitInvestmentGoals = async (data) => {
  console.log('API: 提交投资目标数据', data)
  
  // 模拟网络延迟
  await delay(2000)
  
  // 根据用户输入生成不同的方案建议
  const riskLevel = getRiskLevel(data.riskTolerance, data.expectedReturn)
  const strategy = getStrategy(riskLevel, data.duration)
  
  return {
    success: true,
    data: {
      riskLevel,
      strategy,
      assetAllocation: generateAssetAllocation(riskLevel),
      backtest: generateBacktestData(strategy),
      comparison: generateComparisonData(strategy),
      recommendation: generateRecommendation(data)
    }
  }
}

// 根据风险承受能力和期望收益确定风险等级
const getRiskLevel = (riskTolerance, expectedReturn) => {
  const riskValue = parseFloat(riskTolerance.replace('%', ''))
  const returnValue = parseFloat(expectedReturn.replace('%', ''))
  
  if (riskValue <= 5 && returnValue <= 5) {
    return 'conservative' // 保守型
  } else if (riskValue <= 12 && returnValue <= 12) {
    return 'moderate' // 稳健型
  } else {
    return 'aggressive' // 积极型
  }
}

// 根据风险等级和投资时长确定策略
const getStrategy = (riskLevel, duration) => {
  const strategies = {
    conservative: {
      name: '保守型标准策略',
      description: '以稳健收益为主，适合风险承受能力较低的投资者'
    },
    moderate: {
      name: '稳健型标准策略', 
      description: '平衡风险与收益，适合中等风险承受能力的投资者'
    },
    aggressive: {
      name: '积极型标准策略',
      description: '追求较高收益，适合风险承受能力较强的投资者'
    }
  }
  
  return strategies[riskLevel]
}

// 生成资产配置建议
const generateAssetAllocation = (riskLevel) => {
  const allocations = {
    conservative: {
      low: { conservative: 80, risk: 20 },
      medium: { conservative: 70, risk: 30 },
      high: { conservative: 60, risk: 40 }
    },
    moderate: {
      low: { conservative: 60, risk: 40 },
      medium: { conservative: 50, risk: 50 },
      high: { conservative: 40, risk: 60 }
    },
    aggressive: {
      low: { conservative: 40, risk: 60 },
      medium: { conservative: 30, risk: 70 },
      high: { conservative: 20, risk: 80 }
    }
  }
  
  return allocations[riskLevel]
}

// 生成回测数据
const generateBacktestData = (strategy) => {
  const backtestData = {
    conservative: {
      period: '2018-06-30~2023-06-30',
      annualReturn: 3.54,
      maxDrawdown: -2.11,
      benchmarkReturn: -3.35,
      benchmarkDrawdown: -2.98,
      chartData: {
        strategy: [
          { date: '2018-06', value: 100 },
          { date: '2019-06', value: 103.5 },
          { date: '2020-06', value: 107.2 },
          { date: '2021-06', value: 111.1 },
          { date: '2022-06', value: 115.2 },
          { date: '2023-06', value: 119.5 }
        ],
        benchmark: [
          { date: '2018-06', value: 100 },
          { date: '2019-06', value: 98.5 },
          { date: '2020-06', value: 96.8 },
          { date: '2021-06', value: 95.2 },
          { date: '2022-06', value: 93.8 },
          { date: '2023-06', value: 92.1 }
        ]
      }
    },
    moderate: {
      period: '2018-06-30~2023-06-30',
      annualReturn: 5.82,
      maxDrawdown: -4.56,
      benchmarkReturn: -1.23,
      benchmarkDrawdown: -5.67,
      chartData: {
        strategy: [
          { date: '2018-06', value: 100 },
          { date: '2019-06', value: 105.8 },
          { date: '2020-06', value: 112.1 },
          { date: '2021-06', value: 118.9 },
          { date: '2022-06', value: 126.2 },
          { date: '2023-06', value: 134.1 }
        ],
        benchmark: [
          { date: '2018-06', value: 100 },
          { date: '2019-06', value: 99.2 },
          { date: '2020-06', value: 97.8 },
          { date: '2021-06', value: 96.5 },
          { date: '2022-06', value: 95.1 },
          { date: '2023-06', value: 93.8 }
        ]
      }
    },
    aggressive: {
      period: '2018-06-30~2023-06-30',
      annualReturn: 8.95,
      maxDrawdown: -8.23,
      benchmarkReturn: 2.15,
      benchmarkDrawdown: -12.45,
      chartData: {
        strategy: [
          { date: '2018-06', value: 100 },
          { date: '2019-06', value: 109.0 },
          { date: '2020-06', value: 119.2 },
          { date: '2021-06', value: 130.8 },
          { date: '2022-06', value: 143.9 },
          { date: '2023-06', value: 158.7 }
        ],
        benchmark: [
          { date: '2018-06', value: 100 },
          { date: '2019-06', value: 102.2 },
          { date: '2020-06', value: 104.5 },
          { date: '2021-06', value: 106.9 },
          { date: '2022-06', value: 109.4 },
          { date: '2023-06', value: 112.1 }
        ]
      }
    }
  }
  
  return backtestData.conservative // 默认返回保守型数据
}

// 生成对比数据
const generateComparisonData = (strategy) => {
  return {
    periods: ['持有6个月', '持有1年', '持有2年', '持有3年'],
    strategyPerformance: [12.11, 12.11, 15.11, 15.11],
    benchmarkPerformance: [99.44, 99.44, 100, 100],
    note: '历史数据显示，平均收益基准表现会受到市场因素影响较大，中长期表现相对稳健'
  }
}

// 生成投资建议
const generateRecommendation = (data) => {
  const recommendations = []
  
  // 根据投资时长给出建议
  if (data.duration === '6months' || data.duration === '6m-1y') {
    recommendations.push('建议配置更多短期理财产品，确保资金流动性')
  } else if (data.duration === '1y-3y') {
    recommendations.push('建议采用稳健配置策略，平衡风险与收益')
  } else {
    recommendations.push('建议采用长期投资策略，可适当配置成长性资产')
  }
  
  // 根据风险承受能力给出建议
  const riskValue = parseFloat(data.riskTolerance.replace('%', ''))
  if (riskValue <= 5) {
    recommendations.push('您的风险承受能力较低，建议以稳健型产品为主')
  } else if (riskValue <= 12) {
    recommendations.push('您的风险承受能力适中，可适当配置权益类资产')
  } else {
    recommendations.push('您的风险承受能力较强，可考虑配置更多成长型资产')
  }
  
  return recommendations
}

// 提交完整的心愿账本创建请求
export const createWishAccount = async (data) => {
  console.log('API: 创建心愿账本', data)
  
  // 模拟网络延迟
  await delay(1500)
  
  return {
    success: true,
    data: {
      accountId: Date.now().toString(),
      accountName: data.accountName,
      status: 'created',
      message: '心愿账本创建成功！'
    }
  }
}

// 获取产品配置建议
export const getProductAllocation = async (strategyData) => {
  console.log('API: 获取产品配置建议', strategyData)
  
  // 模拟网络延迟
  await delay(1000)
  
  return {
    success: true,
    data: {
      products: [
        {
          id: 1,
          name: '货币基金A',
          type: 'money_market',
          allocation: 30,
          expectedReturn: '2.5%',
          riskLevel: 'low'
        },
        {
          id: 2,
          name: '债券基金B',
          type: 'bond',
          allocation: 40,
          expectedReturn: '4.2%',
          riskLevel: 'low'
        },
        {
          id: 3,
          name: '混合基金C',
          type: 'hybrid',
          allocation: 20,
          expectedReturn: '8.5%',
          riskLevel: 'medium'
        },
        {
          id: 4,
          name: '股票基金D',
          type: 'equity',
          allocation: 10,
          expectedReturn: '12.8%',
          riskLevel: 'high'
        }
      ],
      totalAllocation: 100,
      expectedPortfolioReturn: '5.8%',
      riskLevel: 'moderate'
    }
  }
}
