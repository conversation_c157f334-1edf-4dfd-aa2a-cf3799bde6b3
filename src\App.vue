<template>
  <div class="page-container">
    <van-nav-bar
      title="我的投资账本"
      left-arrow
      @click-left="onBack"
      right-text="..."
      class="nav-bar"
    />
    <van-tabs v-model:active="active" class="tabs">
      <van-tab title="账本列表">
        <!-- 账本列表内容，可后续补充 -->
      </van-tab>
      <van-tab title="服务记录">
        <div class="empty-block">
          <img src="https://img.yzcdn.cn/vant/empty-image-default.png" class="empty-img" alt="暂无服务记录" />
          <div class="empty-text">暂无服务记录</div>
        </div>
        <div class="bottom-btn-block">
          <van-button block type="warning" @click="onService">预约理财师进行服务</van-button>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { showToast } from 'vant';
import 'vant/lib/index.css';

const active = ref(1);
const onBack = () => window.history.back();
const onService = () => showToast('预约理财师功能待实现');
</script>

<style scoped>
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  max-width: 100vw;
  background: #fff;
  overflow-x: hidden;
}
.nav-bar {
  flex-shrink: 0;
  background: #f44;
  color: #fff;
}
.tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
.empty-block {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  margin-bottom: 80px;
}
.empty-img {
  width: 180px;
  margin-bottom: 16px;
}
.empty-text {
  color: #888;
  font-size: 16px;
  margin-bottom: 32px;
}
.bottom-btn-block {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 16px 16px 24px 16px;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
}
</style>
