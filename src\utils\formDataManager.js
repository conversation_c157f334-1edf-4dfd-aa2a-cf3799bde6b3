/**
 * 表单数据管理工具
 * 用于管理创建账本流程中的表单数据
 */

const STORAGE_KEY = 'createAccountFormData'

// 默认表单数据结构
const defaultFormData = {
  accountType: '', // 账本类型：holding(持仓账本) | wishlist(心愿账本)
  accountName: '',
  createTime: '',
  step1: {
    investmentPeriod: '' // 投资期限
  },
  step2: {
    selectedProducts: [] // 选择的产品
  },
  step3: {
    riskLevel: '', // 风险等级
    targetAmount: 0, // 目标金额
    monthlyInvestment: 0 // 月投资额
  }
}

/**
 * 保存表单数据到本地存储
 * @param {Object} data - 要保存的数据
 */
export const saveFormData = (data) => {
  try {
    const existingData = getFormData()
    const mergedData = { ...existingData, ...data }
    localStorage.setItem(STORAGE_KEY, JSON.stringify(mergedData))
    return true
  } catch (error) {
    console.error('Failed to save form data:', error)
    return false
  }
}

/**
 * 从本地存储获取表单数据
 * @returns {Object} 表单数据
 */
export const getFormData = () => {
  try {
    const data = localStorage.getItem(STORAGE_KEY)
    if (data) {
      return { ...defaultFormData, ...JSON.parse(data) }
    }
    return { ...defaultFormData }
  } catch (error) {
    console.error('Failed to get form data:', error)
    return { ...defaultFormData }
  }
}

/**
 * 更新特定步骤的数据
 * @param {number} step - 步骤号 (0=基本信息, 1, 2, 3)
 * @param {Object} stepData - 步骤数据
 */
export const updateStepData = (step, stepData) => {
  const formData = getFormData()
  if (step === 0) {
    // 步骤0用于更新基本信息（如账本名称）
    Object.assign(formData, stepData)
  } else {
    formData[`step${step}`] = { ...formData[`step${step}`], ...stepData }
  }
  return saveFormData(formData)
}

/**
 * 获取特定步骤的数据
 * @param {number} step - 步骤号 (1, 2, 3)
 * @returns {Object} 步骤数据
 */
export const getStepData = (step) => {
  const formData = getFormData()
  return formData[`step${step}`] || {}
}

/**
 * 清除表单数据
 */
export const clearFormData = () => {
  try {
    localStorage.removeItem(STORAGE_KEY)
    return true
  } catch (error) {
    console.error('Failed to clear form data:', error)
    return false
  }
}

/**
 * 初始化表单数据
 * @param {string} accountType - 账本类型
 */
export const initFormData = (accountType) => {
  const now = new Date()
  const accountName = accountType === 'holding' 
    ? `持仓账本#${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}`
    : `心愿账本#${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}`
  
  const initialData = {
    accountType,
    accountName,
    createTime: now.toISOString()
  }
  
  return saveFormData(initialData)
}

/**
 * 验证表单数据是否完整
 * @returns {Object} 验证结果 { isValid: boolean, missingFields: string[] }
 */
export const validateFormData = () => {
  const formData = getFormData()
  const missingFields = []
  
  // 检查必填字段
  if (!formData.accountType) missingFields.push('账本类型')
  if (!formData.accountName) missingFields.push('账本名称')
  if (!formData.step1.investmentPeriod) missingFields.push('投资期限')
  
  // 根据账本类型检查不同的必填字段
  if (formData.accountType === 'holding') {
    if (!formData.step2.selectedProducts || formData.step2.selectedProducts.length === 0) {
      missingFields.push('持仓产品')
    }
  } else if (formData.accountType === 'wishlist') {
    if (!formData.step3.riskLevel) missingFields.push('风险等级')
    if (!formData.step3.targetAmount) missingFields.push('目标金额')
  }
  
  return {
    isValid: missingFields.length === 0,
    missingFields
  }
}

/**
 * 格式化表单数据为API提交格式
 * @returns {Object} 格式化后的数据
 */
export const formatForSubmission = () => {
  const formData = getFormData()
  
  return {
    account_type: formData.accountType,
    account_name: formData.accountName,
    create_time: formData.createTime,
    investment_period: formData.step1.investmentPeriod,
    selected_products: formData.step2.selectedProducts,
    risk_level: formData.step3.riskLevel,
    target_amount: formData.step3.targetAmount,
    monthly_investment: formData.step3.monthlyInvestment
  }
}

/**
 * 提交表单数据到后端API
 * @returns {Promise} API响应
 */
export const submitFormData = async () => {
  const validation = validateFormData()
  if (!validation.isValid) {
    throw new Error(`表单数据不完整，缺少：${validation.missingFields.join('、')}`)
  }
  
  const submitData = formatForSubmission()
  
  // 这里应该调用实际的API
  // const response = await fetch('/api/accounts', {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/json'
  //   },
  //   body: JSON.stringify(submitData)
  // })
  // return response.json()
  
  // 模拟API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        data: {
          id: Date.now(),
          ...submitData
        },
        message: '账本创建成功'
      })
    }, 1000)
  })
}
