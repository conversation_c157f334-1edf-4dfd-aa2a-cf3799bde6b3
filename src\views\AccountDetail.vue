<template>
  <div class="account-detail-page">
    <van-nav-bar
      title="我的账本"
      left-arrow
      @click-left="onBack"
      class="nav-bar"
    />

    <div class="page-content" v-if="!loading">
      <!-- 账本信息卡片 -->
      <div class="account-card">
        <div class="account-header">
          <h2 class="account-name">{{ accountDetail.accountName }}</h2>
          <span class="view-history">查看历史记录</span>
        </div>

        <div class="account-stats">
          <div class="stat-item">
            <span class="stat-label">总资产</span>
            <span class="stat-value">{{ accountDetail.totalAssets }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">账本数据期</span>
            <span class="stat-value">{{ accountDetail.dataDate }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">基金数量</span>
            <span class="stat-value">{{ accountDetail.fundCount }}只</span>
          </div>
        </div>
      </div>

      <!-- 投资目标 -->
      <div class="investment-goals">
        <h3 class="section-title">投资目标</h3>
        <div class="goals-tags">
          <span
            v-for="goal in accountDetail.investmentGoals"
            :key="goal"
            class="goal-tag"
          >
            {{ goal }}
          </span>
        </div>
      </div>

      <!-- 基金列表 -->
      <div class="funds-section">
        <div class="funds-header">
          <h3 class="section-title">基金简称</h3>
          <span class="holdings-ratio">持仓比例/金额</span>
        </div>

        <div class="funds-list">
          <div
            v-for="fund in accountDetail.funds"
            :key="fund.id"
            class="fund-item"
          >
            <div class="fund-info">
              <div class="fund-name">{{ fund.name }}</div>
              <div class="fund-code">{{ fund.code }}</div>
              <div class="fund-tag">中高风险</div>
            </div>
            <div class="fund-stats">
              <div class="fund-ratio">{{ fund.ratio }}%</div>
              <div class="fund-amount">{{ fund.amount }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="bottom-actions">
        <van-button class="adjust-btn" @click="adjustHoldings">
          调整持仓
        </van-button>
        <van-button class="analyze-btn" @click="analyzeDiagnosis">
          诊断分析
        </van-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading size="24px" vertical>加载中...</van-loading>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import { getAccountDetail } from '../api/mockApi.js'

const router = useRouter()
const route = useRoute()

// 页面状态
const loading = ref(true)
const accountDetail = ref({})

// 获取账本详情
const loadAccountDetail = async () => {
  try {
    loading.value = true
    const accountId = route.params.id
    const response = await getAccountDetail(accountId)

    if (response.success) {
      accountDetail.value = response.data
    } else {
      showToast.fail('获取账本详情失败')
    }
  } catch (error) {
    console.error('获取账本详情失败:', error)
    showToast.fail('获取账本详情失败')
  } finally {
    loading.value = false
  }
}

// 返回
const onBack = () => {
  router.push({ name: 'AccountList' })
}

// 调整持仓
const adjustHoldings = () => {
  showToast('调整持仓功能开发中...')
}

// 诊断分析
const analyzeDiagnosis = () => {
  router.push({
    name: 'AccountAnalysis',
    params: { id: route.params.id }
  })
}

// 页面初始化
onMounted(() => {
  loadAccountDetail()
})
</script>

<style scoped>
.account-detail-page {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.nav-bar {
  background: #ff6b6b !important;
  color: #fff;
  flex-shrink: 0;
}

.page-content {
  flex: 1;
  padding-bottom: 80px;
  overflow-y: auto;
}

.loading-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 账本信息卡片 */
.account-card {
  background: #fff;
  margin: 3% 4% 4% 4%;
  padding: 5%;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4%;
}

.account-name {
  font-size: 18px;
  color: #333;
  font-weight: 600;
  margin: 0;
}

.view-history {
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
}

.account-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2%;
}

.stat-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 投资目标 */
.investment-goals {
  background: #fff;
  margin: 0 4% 4% 4%;
  padding: 4%;
  border-radius: 12px;
}

.section-title {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  margin: 0 0 3% 0;
}

.goals-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 2%;
}

.goal-tag {
  background: #fff2e8;
  color: #ff8c00;
  padding: 2% 3%;
  border-radius: 16px;
  font-size: 12px;
  margin-bottom: 2%;
}

/* 基金列表 */
.funds-section {
  background: #fff;
  margin: 0 4% 4% 4%;
  border-radius: 12px;
  overflow: hidden;
}

.funds-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4%;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.holdings-ratio {
  font-size: 12px;
  color: #666;
}

.funds-list {
  padding: 0 4%;
}

.fund-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4% 0;
  border-bottom: 1px solid #f8f8f8;
}

.fund-item:last-child {
  border-bottom: none;
}

.fund-info {
  flex: 1;
}

.fund-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 1%;
}

.fund-code {
  font-size: 12px;
  color: #666;
  margin-bottom: 2%;
}

.fund-tag {
  display: inline-block;
  background: #1890ff;
  color: #fff;
  padding: 1% 2%;
  border-radius: 4px;
  font-size: 10px;
}

.fund-stats {
  text-align: right;
}

.fund-ratio {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 1%;
}

.fund-amount {
  font-size: 12px;
  color: #666;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  gap: 3%;
  padding: 4%;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
}

.adjust-btn {
  flex: 1;
  background: #f5f5f5 !important;
  border: none !important;
  color: #666 !important;
  height: 44px !important;
  border-radius: 8px !important;
}

.analyze-btn {
  flex: 1;
  background: #ff6b6b !important;
  border: none !important;
  color: #fff !important;
  height: 44px !important;
  border-radius: 8px !important;
}

:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}
</style>
