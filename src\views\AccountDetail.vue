<template>
  <div class="page-container">
    <van-nav-bar
      :title="accountName"
      left-arrow
      @click-left="onBack"
      right-text="..."
      class="nav-bar"
    />
    <van-tabs v-model:active="active" class="tabs">
      <van-tab title="账本列表">
        <!-- 账本列表内容，可后续补充 -->
      </van-tab>
      <van-tab title="服务记录">
        <div class="service-content">
          <div class="empty-block">
            <div class="empty-illustration">
              <!-- 自定义空状态插图，匹配UI设计 -->
              <svg width="100%" height="100%" viewBox="0 0 240 180" class="empty-svg">
                <!-- 文档背景 -->
                <rect x="60" y="40" width="120" height="100" rx="8" fill="#E8F4FD" stroke="#B8D4F0" stroke-width="2"/>
                <!-- 文档内容线条 -->
                <rect x="75" y="55" width="60" height="4" rx="2" fill="#B8D4F0"/>
                <rect x="75" y="65" width="45" height="4" rx="2" fill="#B8D4F0"/>
                <rect x="75" y="75" width="70" height="4" rx="2" fill="#B8D4F0"/>
                <rect x="75" y="85" width="35" height="4" rx="2" fill="#B8D4F0"/>
                <!-- 对话气泡 -->
                <circle cx="200" cy="70" r="25" fill="#B8D4F0" opacity="0.8"/>
                <circle cx="195" cy="68" r="3" fill="#fff"/>
                <circle cx="200" cy="68" r="3" fill="#fff"/>
                <circle cx="205" cy="68" r="3" fill="#fff"/>
                <!-- 装饰星星 -->
                <polygon points="90,25 92,31 98,31 93,35 95,41 90,37 85,41 87,35 82,31 88,31" fill="#B8D4F0" opacity="0.6"/>
                <polygon points="210,100 212,106 218,106 213,110 215,116 210,112 205,116 207,110 202,106 208,106" fill="#B8D4F0" opacity="0.6"/>
                <!-- 文档底部 -->
                <rect x="60" y="130" width="120" height="20" rx="8" fill="#D1E7F8"/>
              </svg>
            </div>
            <div class="empty-text">暂无服务记录</div>
          </div>
          <div class="bottom-btn-block">
            <van-button block class="service-btn" @click="onService">预约理财师进行服务</van-button>
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()
const route = useRoute()

const active = ref(1)

// 从路由参数获取账本名称
const accountName = computed(() => {
  return route.query.name || '我的投资账本'
})

const onBack = () => {
  router.back()
}

const onService = () => {
  showToast('预约理财师功能待实现')
}
</script>

<style scoped>
/* 页面容器 - 100%宽度适配 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  max-width: 100vw;
  background: #f8f9fa;
  overflow-x: hidden;
  position: relative;
}

/* 导航栏 - 匹配UI图红色 */
.nav-bar {
  flex-shrink: 0;
  background: #ff6b6b !important;
  color: #fff;
  position: relative;
  z-index: 100;
}

/* 标签页容器 */
.tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: #f8f9fa;
}

/* 服务记录内容区域 */
.service-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: #f8f9fa;
}

/* 空状态区域 - 使用百分比定位 */
.empty-block {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5% 8%;
  min-height: calc(100vh - 200px);
  position: relative;
}

/* 空状态插图容器 */
.empty-illustration {
  width: 60%;
  max-width: 240px;
  min-width: 180px;
  height: auto;
  aspect-ratio: 4/3;
  margin-bottom: 6%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* SVG插图样式 */
.empty-svg {
  width: 100%;
  height: 100%;
  max-width: 240px;
  max-height: 180px;
}

/* 空状态文字 */
.empty-text {
  color: #969799;
  font-size: 16px;
  font-weight: 400;
  text-align: center;
  margin-top: 4%;
  line-height: 1.4;
}

/* 底部按钮区域 - 使用百分比定位 */
.bottom-btn-block {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 4% 4% 6% 4%;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
  border-top: 1px solid #f0f0f0;
}

/* 服务按钮样式 - 匹配UI图橙色 */
.service-btn {
  background: linear-gradient(135deg, #ff9500 0%, #ff7700 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  height: 48px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(255, 149, 0, 0.3);
  transition: all 0.3s ease;
}

.service-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(255, 149, 0, 0.4);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .empty-illustration {
    width: 70%;
    margin-bottom: 8%;
  }

  .empty-text {
    font-size: 14px;
  }

  .bottom-btn-block {
    padding: 5% 5% 8% 5%;
  }
}

@media (min-width: 768px) {
  .empty-illustration {
    width: 40%;
    max-width: 200px;
  }

  .bottom-btn-block {
    padding: 3% 6% 4% 6%;
  }
}

/* 确保标签页内容区域100%宽度 */
:deep(.van-tabs__content) {
  width: 100%;
  height: 100%;
}

:deep(.van-tab__panel) {
  width: 100%;
  height: 100%;
  padding: 0;
}

/* 标签页标题样式优化 */
:deep(.van-tabs__nav) {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.van-tab) {
  color: #969799;
  font-weight: 400;
}

:deep(.van-tab--active) {
  color: #ff6b6b;
  font-weight: 500;
}

:deep(.van-tabs__line) {
  background: #ff6b6b;
}

/* 导航栏样式优化 */
:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__text) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}
</style>
