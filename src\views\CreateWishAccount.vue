<template>
  <div class="create-wish-account">
    <!-- 顶部导航 -->
    <van-nav-bar
      title="创建王维的心愿账本"
      left-arrow
      @click-left="onBack"
      class="custom-nav-bar"
    >
      <template #right>
        <van-icon name="ellipsis" />
        <van-icon name="question-o" style="margin-left: 8px;" />
      </template>
    </van-nav-bar>

    <!-- 标签页 -->
    <van-tabs v-model:active="activeTab" class="wish-tabs">
      <van-tab title="投资数据目标" name="target"></van-tab>
      <van-tab title="方案建议" name="suggestion"></van-tab>
      <van-tab title="配置资产" name="allocation"></van-tab>
      <van-tab title="预览开户" name="preview"></van-tab>
    </van-tabs>

    <!-- 投资数据目标页面 -->
    <div v-if="activeTab === 'target'" class="tab-content">
      <div class="form-section">
        <!-- 客户姓名 -->
        <div class="form-item">
          <label class="form-label">客户姓名</label>
          <div class="form-value">王维</div>
        </div>

        <!-- 账本名称 -->
        <div class="form-item">
          <label class="form-label">账本名称</label>
          <van-field
            v-model="formData.accountName"
            placeholder="请输入"
            class="custom-field"
          />
        </div>

        <!-- 投资目标 -->
        <div class="form-item">
          <label class="form-label">投资目标</label>
          <van-field
            v-model="formData.investmentGoal"
            placeholder="请输入"
            class="custom-field"
          />
        </div>

        <!-- 计划投资金额 -->
        <div class="form-item">
          <label class="form-label">计划投资金额(万元)</label>
          <van-field
            v-model="formData.plannedAmount"
            placeholder="请输入"
            class="custom-field"
          />
        </div>

        <!-- 计划投资时长 -->
        <div class="form-item">
          <label class="form-label">计划投资时长</label>
          <div class="form-value">
            <span class="help-text">如何科学合理投资?</span>
          </div>
        </div>

        <!-- 投资时长选择 -->
        <div class="duration-options">
          <div 
            v-for="duration in durationOptions" 
            :key="duration.value"
            :class="['duration-option', { active: selectedDuration === duration.value }]"
            @click="selectDuration(duration.value)"
          >
            {{ duration.label }}
          </div>
        </div>

        <!-- 对这笔钱的理想年化收益 -->
        <div class="form-item">
          <label class="form-label">对这笔钱的理想年化收益</label>
        </div>

        <!-- 收益率选择 -->
        <div class="return-options">
          <div 
            v-for="returnRate in returnOptions" 
            :key="returnRate.value"
            :class="['return-option', { active: selectedReturn === returnRate.value }]"
            @click="selectReturn(returnRate.value)"
          >
            <div class="return-rate">{{ returnRate.rate }}</div>
            <div class="return-desc">{{ returnRate.desc }}</div>
          </div>
        </div>

        <!-- 可承受的最大亏损 -->
        <div class="form-item">
          <label class="form-label">可承受的最大亏损</label>
        </div>

        <!-- 亏损承受选择 -->
        <div class="loss-options">
          <div 
            v-for="loss in lossOptions" 
            :key="loss.value"
            :class="['loss-option', { active: selectedLoss === loss.value }]"
            @click="selectLoss(loss.value)"
          >
            {{ loss.label }}
          </div>
        </div>

        <!-- 投资偏好 -->
        <div class="form-item clickable" @click="showPreferencePopup">
          <label class="form-label">投资偏好</label>
          <div class="form-value">
            <span class="preference-text">希望配置有利于长期投资的产品</span>
            <van-icon name="arrow" />
          </div>
        </div>

        <!-- 底部提示 -->
        <div class="bottom-tip">
          本页面仅供参考预测
        </div>
      </div>
    </div>

    <!-- 方案建议页面 -->
    <div v-if="activeTab === 'suggestion'" class="tab-content">
      <div v-if="loading" class="loading-container">
        <van-loading type="spinner" color="#667eea">分析中...</van-loading>
      </div>

      <div v-else class="suggestion-content">
        <!-- 标准配置策略介绍 -->
        <div class="strategy-card">
          <h3 class="strategy-title">标准配置策略介绍</h3>

          <!-- 大类资产比例 -->
          <div class="asset-allocation">
            <h4 class="section-title">大类资产比例</h4>
            <p class="allocation-desc">
              基于您的风险偏好、年龄以及可承受的风险等级，适合您的
              资产配置策略为：以配置稳健收益资产为主，权益资产在市场
              估值相对合理时可适当配置，参考资产配置比例如下：
            </p>

            <!-- 风险适度表格 -->
            <div class="risk-table">
              <div class="table-header">
                <div class="header-cell"></div>
                <div class="header-cell">保守资产占比</div>
                <div class="header-cell">风险资产占比</div>
              </div>
              <div class="table-row">
                <div class="risk-level">低</div>
                <div class="conservative-ratio">20%</div>
                <div class="risk-ratio">80%</div>
              </div>
              <div class="table-row">
                <div class="risk-level">中</div>
                <div class="conservative-ratio">10%</div>
                <div class="risk-ratio">90%</div>
              </div>
              <div class="table-row">
                <div class="risk-level">高</div>
                <div class="conservative-ratio">0</div>
                <div class="risk-ratio">100%</div>
              </div>
            </div>

            <p class="risk-note">
              市场适度：参考基准是基准利率指数
            </p>
          </div>

          <!-- 保守型标准策略回测表现 -->
          <div class="backtest-section">
            <h4 class="section-title">保守型标准策略回测表现</h4>
            <div class="backtest-period">2018-06-30~2023-06-30</div>

            <!-- 回测曲线图 -->
            <div class="chart-container">
              <canvas ref="backtestChart" width="350" height="200" class="backtest-chart"></canvas>
            </div>

            <!-- 回测数据 -->
            <div class="backtest-data" v-if="suggestionData">
              <div class="data-item">
                <span class="data-label">年化收益：</span>
                <span class="data-value positive">{{ suggestionData.backtest.annualReturn }}%</span>
              </div>
              <div class="data-item">
                <span class="data-label">年化最大回撤：</span>
                <span class="data-value negative">{{ suggestionData.backtest.maxDrawdown }}%</span>
              </div>
              <div class="data-item">
                <span class="data-label">基准收益：</span>
                <span class="data-value negative">{{ suggestionData.backtest.benchmarkReturn }}%</span>
              </div>
              <div class="data-item">
                <span class="data-label">基准最大回撤：</span>
                <span class="data-value negative">{{ suggestionData.backtest.benchmarkDrawdown }}%</span>
              </div>
            </div>
          </div>

          <!-- 保守型标准策略资产改进比较 -->
          <div class="improvement-section">
            <h4 class="section-title">保守型标准策略资产改进比较</h4>

            <!-- 图例 -->
            <div class="legend">
              <div class="legend-item">
                <div class="legend-dot" style="background: #1890ff;"></div>
                <span>标准策略</span>
              </div>
              <div class="legend-item">
                <div class="legend-dot" style="background: #faad14;"></div>
                <span>平均收益基准</span>
              </div>
            </div>

            <!-- 柱状图 -->
            <div class="chart-container">
              <canvas ref="comparisonChart" width="350" height="200" class="comparison-chart"></canvas>
            </div>

            <!-- 数据说明 -->
            <p class="improvement-note">
              历史数据显示，平均收益基准表现会受到市场
              因素影响较大，中长期表现相对稳健
            </p>
          </div>
        </div>
      </div>
    </div>

    <div v-if="activeTab === 'allocation'" class="tab-content">
      <div class="placeholder">配置资产内容开发中...</div>
    </div>

    <div v-if="activeTab === 'preview'" class="tab-content">
      <div class="placeholder">预览开户内容开发中...</div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <van-button class="cancel-btn" @click="onCancel">取消</van-button>
      <van-button type="primary" class="next-btn" @click="onNext">
        {{ getNextButtonText() }}
      </van-button>
    </div>

    <!-- 投资偏好弹窗 -->
    <van-popup v-model:show="showPreference" position="bottom" class="preference-popup">
      <div class="popup-header">
        <h3>投资偏好</h3>
        <van-icon name="cross" @click="showPreference = false" />
      </div>
      <div class="preference-options">
        <div
          v-for="preference in preferenceOptions"
          :key="preference.value"
          :class="['preference-option', { active: selectedPreference === preference.value }]"
          @click="selectPreference(preference.value)"
        >
          <div class="preference-title">{{ preference.title }}</div>
          <div class="preference-desc">{{ preference.desc }}</div>
        </div>
      </div>
      <div class="popup-actions">
        <van-button type="primary" block @click="confirmPreference">确认</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { submitInvestmentGoals } from '../api/wishAccount.js'

const router = useRouter()

// 当前活跃标签
const activeTab = ref('target')

// 表单数据
const formData = ref({
  accountName: '',
  investmentGoal: '',
  plannedAmount: ''
})

// 选中的投资时长
const selectedDuration = ref('')

// 投资时长选项
const durationOptions = ref([
  { label: '6个月以内', value: '6months' },
  { label: '6个月-1年', value: '6m-1y' },
  { label: '1年-3年', value: '1y-3y' },
  { label: '3年以上', value: '3y+' }
])

// 选中的收益率
const selectedReturn = ref('')

// 收益率选项
const returnOptions = ref([
  { value: 'conservative', rate: '3%左右', desc: '短期理财 年化2%-4%' },
  { value: 'moderate', rate: '5%左右', desc: '稳健收益 年化5%-8%' },
  { value: 'balanced', rate: '12%左右', desc: '追求收益' },
  { value: 'aggressive', rate: '20%左右', desc: '' }
])

// 选中的亏损承受
const selectedLoss = ref('')

// 亏损承受选项
const lossOptions = ref([
  { label: '3%左右', value: '3%' },
  { label: '5%左右', value: '5%' },
  { label: '12%左右', value: '12%' },
  { label: '20%左右', value: '20%' }
])

// 投资偏好弹窗
const showPreference = ref(false)
const selectedPreference = ref('long-term')

// 加载状态
const loading = ref(false)

// 图表引用
const backtestChart = ref(null)
const comparisonChart = ref(null)

// 方案建议数据
const suggestionData = ref(null)

// 投资偏好选项
const preferenceOptions = ref([
  {
    value: 'long-term',
    title: '长期投资',
    desc: '希望配置有利于长期投资的产品'
  },
  {
    value: 'stable',
    title: '稳健投资',
    desc: '希望配置风险较低的稳健产品'
  },
  {
    value: 'growth',
    title: '成长投资',
    desc: '希望配置有成长潜力的产品'
  },
  {
    value: 'balanced',
    title: '均衡投资',
    desc: '希望配置风险收益均衡的产品'
  }
])

// 选择投资时长
const selectDuration = (value) => {
  selectedDuration.value = value
}

// 选择收益率
const selectReturn = (value) => {
  selectedReturn.value = value
}

// 选择亏损承受
const selectLoss = (value) => {
  selectedLoss.value = value
}

// 返回上一页
const onBack = () => {
  router.back()
}

// 取消
const onCancel = () => {
  router.back()
}

// 显示投资偏好弹窗
const showPreferencePopup = () => {
  showPreference.value = true
}

// 选择投资偏好
const selectPreference = (value) => {
  selectedPreference.value = value
}

// 确认投资偏好
const confirmPreference = () => {
  showPreference.value = false
}

// 绘制回测曲线图
const drawBacktestChart = () => {
  if (!backtestChart.value) return

  const canvas = backtestChart.value
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 模拟回测数据点
  const strategyPoints = [
    { x: 50, y: 150 }, { x: 80, y: 140 }, { x: 110, y: 130 }, { x: 140, y: 125 },
    { x: 170, y: 120 }, { x: 200, y: 115 }, { x: 230, y: 110 }, { x: 260, y: 105 },
    { x: 290, y: 100 }, { x: 320, y: 95 }
  ]

  const benchmarkPoints = [
    { x: 50, y: 160 }, { x: 80, y: 155 }, { x: 110, y: 165 }, { x: 140, y: 170 },
    { x: 170, y: 175 }, { x: 200, y: 180 }, { x: 230, y: 185 }, { x: 260, y: 180 },
    { x: 290, y: 175 }, { x: 320, y: 170 }
  ]

  // 绘制网格线
  ctx.strokeStyle = '#f0f0f0'
  ctx.lineWidth = 1

  // 水平网格线
  for (let i = 0; i <= 4; i++) {
    const y = (height / 4) * i
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()
  }

  // 绘制曲线的函数
  const drawCurve = (points, color) => {
    if (points.length < 2) return

    ctx.strokeStyle = color
    ctx.lineWidth = 2
    ctx.beginPath()

    ctx.moveTo(points[0].x, points[0].y)

    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].x, points[i].y)
    }

    ctx.stroke()
  }

  // 绘制两条曲线
  drawCurve(strategyPoints, '#1890ff')  // 标准策略 - 蓝色
  drawCurve(benchmarkPoints, '#ff4d4f') // 基准 - 红色
}

// 绘制对比柱状图
const drawComparisonChart = () => {
  if (!comparisonChart.value) return

  const canvas = comparisonChart.value
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 柱状图数据
  const periods = ['持有6个月', '持有1年', '持有2年', '持有3年']
  const strategyData = [12.11, 12.11, 15.11, 15.11]
  const benchmarkData = [99.44, 99.44, 100, 100]

  const barWidth = 30
  const groupWidth = 80
  const startX = 40
  const maxValue = 120

  // 绘制柱状图
  periods.forEach((period, index) => {
    const x = startX + index * groupWidth

    // 标准策略柱子 (蓝色)
    const strategyHeight = (strategyData[index] / maxValue) * (height - 60)
    ctx.fillStyle = '#1890ff'
    ctx.fillRect(x, height - 40 - strategyHeight, barWidth, strategyHeight)

    // 平均收益基准柱子 (黄色)
    const benchmarkHeight = (benchmarkData[index] / maxValue) * (height - 60)
    ctx.fillStyle = '#faad14'
    ctx.fillRect(x + barWidth + 5, height - 40 - benchmarkHeight, barWidth, benchmarkHeight)

    // 绘制数值标签
    ctx.fillStyle = '#333'
    ctx.font = '10px Arial'
    ctx.textAlign = 'center'

    // 标准策略数值
    ctx.fillText(strategyData[index].toString(), x + barWidth/2, height - 45 - strategyHeight)

    // 基准数值
    ctx.fillText(benchmarkData[index].toString(), x + barWidth + 5 + barWidth/2, height - 45 - benchmarkHeight)

    // 期间标签
    ctx.fillText(period, x + barWidth + 2.5, height - 10)
  })
}

// 提交投资目标数据到后端
const submitInvestmentData = async () => {
  loading.value = true

  try {
    const requestData = {
      customerName: '王维',
      accountName: formData.value.accountName,
      investmentGoal: formData.value.investmentGoal,
      plannedAmount: formData.value.plannedAmount,
      duration: selectedDuration.value,
      expectedReturn: selectedReturn.value,
      riskTolerance: selectedLoss.value,
      investmentPreference: selectedPreference.value
    }

    console.log('提交投资数据:', requestData)

    // 调用API获取方案建议
    const response = await submitInvestmentGoals(requestData)

    if (response.success) {
      suggestionData.value = response.data

      // 绘制图表
      setTimeout(() => {
        drawBacktestChart()
        drawComparisonChart()
      }, 100)

      showToast('方案分析完成！')
    } else {
      throw new Error(response.message || '获取方案建议失败')
    }

  } catch (error) {
    console.error('提交数据失败:', error)
    showToast('数据提交失败，请重试')
  } finally {
    loading.value = false
  }
}

// 获取下一步按钮文本
const getNextButtonText = () => {
  const buttonTexts = {
    'target': '下一步',
    'suggestion': '下一步',
    'allocation': '下一步',
    'preview': '创建账本'
  }
  return buttonTexts[activeTab.value] || '下一步'
}

// 下一步
const onNext = async () => {
  // 验证当前步骤的必填项
  if (activeTab.value === 'target') {
    if (!formData.value.accountName) {
      showToast('请输入账本名称')
      return
    }
    if (!selectedDuration.value) {
      showToast('请选择投资时长')
      return
    }
    if (!selectedReturn.value) {
      showToast('请选择理想收益率')
      return
    }
    if (!selectedLoss.value) {
      showToast('请选择可承受的最大亏损')
      return
    }

    // 切换到方案建议页面并提交数据
    activeTab.value = 'suggestion'
    await submitInvestmentData()

  } else if (activeTab.value === 'suggestion') {
    activeTab.value = 'allocation'
  } else if (activeTab.value === 'allocation') {
    activeTab.value = 'preview'
  } else {
    // 最后一步，提交数据
    console.log('提交心愿账本数据:', {
      ...formData.value,
      duration: selectedDuration.value,
      returnRate: selectedReturn.value,
      lossAcceptance: selectedLoss.value,
      preference: selectedPreference.value
    })
    showToast('心愿账本创建成功！')
    // 可以跳转到成功页面或返回账本列表
    setTimeout(() => {
      router.push('/')
    }, 1500)
  }
}
</script>

<style scoped>
.create-wish-account {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.custom-nav-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar .van-icon) {
  color: #fff;
}

.wish-tabs {
  background: #fff;
  margin-bottom: 2%;
}

:deep(.van-tabs__nav) {
  background: #fff;
}

:deep(.van-tab) {
  font-size: 14px;
}

:deep(.van-tabs__line) {
  background: #667eea;
}

.tab-content {
  padding: 0 4%;
}

.form-section {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4% 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item.clickable {
  cursor: pointer;
}

.form-item.clickable:hover {
  background: #f8f9fa;
}

.form-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-value {
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 2%;
}

.help-text {
  color: #667eea;
  font-size: 12px;
}

.preference-text {
  color: #333;
}

.custom-field {
  border: none;
  padding: 0;
  text-align: right;
}

:deep(.van-field__control) {
  text-align: right;
  color: #333;
}

.duration-options {
  display: flex;
  gap: 3%;
  margin: 4% 0;
}

.duration-option {
  flex: 1;
  padding: 3% 0;
  text-align: center;
  background: #f8f9fa;
  border-radius: 20px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.duration-option.active {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.return-options {
  display: flex;
  flex-wrap: wrap;
  gap: 3%;
  margin: 4% 0;
}

.return-option {
  width: 48%;
  padding: 4%;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 3%;
}

.return-option.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.return-rate {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2%;
}

.return-desc {
  font-size: 10px;
  color: #999;
  line-height: 1.4;
}

.loss-options {
  display: flex;
  gap: 3%;
  margin: 4% 0;
}

.loss-option {
  flex: 1;
  padding: 3% 0;
  text-align: center;
  background: #f8f9fa;
  border-radius: 20px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.loss-option.active {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.bottom-tip {
  text-align: center;
  font-size: 12px;
  color: #999;
  margin-top: 6%;
  padding-top: 4%;
  border-top: 1px solid #f0f0f0;
}

.placeholder {
  padding: 10%;
  text-align: center;
  color: #999;
  font-size: 14px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 4%;
  padding: 4%;
  background: #fff;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn {
  flex: 1;
  height: 44px;
  border: 1px solid #d9d9d9;
  background: #fff;
  color: #666;
  border-radius: 22px;
}

.next-btn {
  flex: 2;
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 22px;
  color: #fff;
  font-weight: 600;
}

/* 投资偏好弹窗样式 */
.preference-popup {
  border-radius: 16px 16px 0 0;
}

:deep(.van-popup) {
  max-height: 70vh;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4% 4% 2% 4%;
  border-bottom: 1px solid #f0f0f0;
}

.popup-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.popup-header .van-icon {
  font-size: 18px;
  color: #999;
  cursor: pointer;
}

.preference-options {
  padding: 4%;
  max-height: 50vh;
  overflow-y: auto;
}

.preference-option {
  padding: 4%;
  margin-bottom: 3%;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preference-option.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.preference-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2%;
}

.preference-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.popup-actions {
  padding: 4%;
  border-top: 1px solid #f0f0f0;
}

.popup-actions .van-button {
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
}

/* 方案建议页面样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.suggestion-content {
  padding-bottom: 4%;
}

.strategy-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 4%;
}

.strategy-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin: 0 0 4% 0;
  padding: 3% 0;
  background: #f8f9fa;
  border-radius: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 4% 0 3% 0;
}

.allocation-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 4%;
}

.risk-table {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  margin: 3% 0;
}

.table-header {
  display: flex;
  background: #fafafa;
  font-weight: 600;
  font-size: 12px;
}

.table-row {
  display: flex;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
}

.header-cell {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #333;
}

.risk-level {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #333;
  font-weight: 500;
}

.conservative-ratio {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #1890ff;
  font-weight: 600;
}

.risk-ratio {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #52c41a;
  font-weight: 600;
}

.risk-note {
  font-size: 11px;
  color: #999;
  margin: 3% 0;
}

.backtest-section {
  margin-top: 6%;
  padding-top: 4%;
  border-top: 1px solid #f0f0f0;
}

.backtest-period {
  font-size: 12px;
  color: #666;
  margin-bottom: 3%;
}

.chart-container {
  margin: 4% 0;
  text-align: center;
}

.backtest-chart,
.comparison-chart {
  width: 100%;
  height: auto;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.backtest-data {
  display: flex;
  flex-wrap: wrap;
  gap: 4%;
  margin: 4% 0;
}

.data-item {
  flex: 1;
  min-width: 45%;
  font-size: 12px;
  margin-bottom: 2%;
}

.data-label {
  color: #666;
}

.data-value {
  font-weight: 600;
}

.data-value.positive {
  color: #52c41a;
}

.data-value.negative {
  color: #ff4d4f;
}

.improvement-section {
  margin-top: 6%;
  padding-top: 4%;
  border-top: 1px solid #f0f0f0;
}

.legend {
  display: flex;
  gap: 6%;
  margin: 3% 0;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 2%;
  font-size: 12px;
  color: #666;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.improvement-note {
  font-size: 11px;
  color: #999;
  line-height: 1.5;
  margin-top: 3%;
  text-align: center;
}
</style>
