<template>
  <div class="create-wish-account">
    <!-- 顶部导航 -->
    <van-nav-bar
      title="创建王维的心愿账本"
      left-arrow
      @click-left="onBack"
      class="custom-nav-bar"
    >
      <template #right>
        <van-icon name="ellipsis" />
        <van-icon name="question-o" style="margin-left: 8px;" />
      </template>
    </van-nav-bar>

    <!-- 标签页 -->
    <van-tabs v-model:active="activeTab" class="wish-tabs">
      <van-tab title="投资数据目标" name="target"></van-tab>
      <van-tab title="方案建议" name="suggestion"></van-tab>
      <van-tab title="配置资产" name="allocation"></van-tab>
      <van-tab title="预览开户" name="preview"></van-tab>
    </van-tabs>

    <!-- 投资数据目标页面 -->
    <div v-if="activeTab === 'target'" class="tab-content">
      <div class="form-section">
        <!-- 客户姓名 -->
        <div class="form-item">
          <label class="form-label">客户姓名</label>
          <div class="form-value">王维</div>
        </div>

        <!-- 账本名称 -->
        <div class="form-item">
          <label class="form-label">账本名称</label>
          <van-field
            v-model="formData.accountName"
            placeholder="请输入"
            class="custom-field"
          />
        </div>

        <!-- 投资目标 -->
        <div class="form-item">
          <label class="form-label">投资目标</label>
          <van-field
            v-model="formData.investmentGoal"
            placeholder="请输入"
            class="custom-field"
          />
        </div>

        <!-- 计划投资金额 -->
        <div class="form-item">
          <label class="form-label">计划投资金额(万元)</label>
          <van-field
            v-model="formData.plannedAmount"
            placeholder="请输入"
            class="custom-field"
          />
        </div>

        <!-- 计划投资时长 -->
        <div class="form-item">
          <label class="form-label">计划投资时长</label>
          <div class="form-value">
            <span class="help-text">如何科学合理投资?</span>
          </div>
        </div>

        <!-- 投资时长选择 -->
        <div class="duration-options">
          <div 
            v-for="duration in durationOptions" 
            :key="duration.value"
            :class="['duration-option', { active: selectedDuration === duration.value }]"
            @click="selectDuration(duration.value)"
          >
            {{ duration.label }}
          </div>
        </div>

        <!-- 对这笔钱的理想年化收益 -->
        <div class="form-item">
          <label class="form-label">对这笔钱的理想年化收益</label>
        </div>

        <!-- 收益率选择 -->
        <div class="return-options">
          <div 
            v-for="returnRate in returnOptions" 
            :key="returnRate.value"
            :class="['return-option', { active: selectedReturn === returnRate.value }]"
            @click="selectReturn(returnRate.value)"
          >
            <div class="return-rate">{{ returnRate.rate }}</div>
            <div class="return-desc">{{ returnRate.desc }}</div>
          </div>
        </div>

        <!-- 可承受的最大亏损 -->
        <div class="form-item">
          <label class="form-label">可承受的最大亏损</label>
        </div>

        <!-- 亏损承受选择 -->
        <div class="loss-options">
          <div 
            v-for="loss in lossOptions" 
            :key="loss.value"
            :class="['loss-option', { active: selectedLoss === loss.value }]"
            @click="selectLoss(loss.value)"
          >
            {{ loss.label }}
          </div>
        </div>

        <!-- 投资偏好 -->
        <div class="form-item clickable" @click="showPreferencePopup">
          <label class="form-label">投资偏好</label>
          <div class="form-value">
            <span class="preference-text">希望配置有利于长期投资的产品</span>
            <van-icon name="arrow" />
          </div>
        </div>

        <!-- 底部提示 -->
        <div class="bottom-tip">
          本页面仅供参考预测
        </div>
      </div>
    </div>

    <!-- 其他标签页内容占位 -->
    <div v-if="activeTab === 'suggestion'" class="tab-content">
      <div class="placeholder">方案建议内容开发中...</div>
    </div>

    <div v-if="activeTab === 'allocation'" class="tab-content">
      <div class="placeholder">配置资产内容开发中...</div>
    </div>

    <div v-if="activeTab === 'preview'" class="tab-content">
      <div class="placeholder">预览开户内容开发中...</div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <van-button class="cancel-btn" @click="onCancel">取消</van-button>
      <van-button type="primary" class="next-btn" @click="onNext">
        {{ getNextButtonText() }}
      </van-button>
    </div>

    <!-- 投资偏好弹窗 -->
    <van-popup v-model:show="showPreference" position="bottom" class="preference-popup">
      <div class="popup-header">
        <h3>投资偏好</h3>
        <van-icon name="cross" @click="showPreference = false" />
      </div>
      <div class="preference-options">
        <div
          v-for="preference in preferenceOptions"
          :key="preference.value"
          :class="['preference-option', { active: selectedPreference === preference.value }]"
          @click="selectPreference(preference.value)"
        >
          <div class="preference-title">{{ preference.title }}</div>
          <div class="preference-desc">{{ preference.desc }}</div>
        </div>
      </div>
      <div class="popup-actions">
        <van-button type="primary" block @click="confirmPreference">确认</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 当前活跃标签
const activeTab = ref('target')

// 表单数据
const formData = ref({
  accountName: '',
  investmentGoal: '',
  plannedAmount: ''
})

// 选中的投资时长
const selectedDuration = ref('')

// 投资时长选项
const durationOptions = ref([
  { label: '6个月以内', value: '6months' },
  { label: '6个月-1年', value: '6m-1y' },
  { label: '1年-3年', value: '1y-3y' },
  { label: '3年以上', value: '3y+' }
])

// 选中的收益率
const selectedReturn = ref('')

// 收益率选项
const returnOptions = ref([
  { value: 'conservative', rate: '3%左右', desc: '短期理财 年化2%-4%' },
  { value: 'moderate', rate: '5%左右', desc: '稳健收益 年化5%-8%' },
  { value: 'balanced', rate: '12%左右', desc: '追求收益' },
  { value: 'aggressive', rate: '20%左右', desc: '' }
])

// 选中的亏损承受
const selectedLoss = ref('')

// 亏损承受选项
const lossOptions = ref([
  { label: '3%左右', value: '3%' },
  { label: '5%左右', value: '5%' },
  { label: '12%左右', value: '12%' },
  { label: '20%左右', value: '20%' }
])

// 投资偏好弹窗
const showPreference = ref(false)
const selectedPreference = ref('long-term')

// 投资偏好选项
const preferenceOptions = ref([
  {
    value: 'long-term',
    title: '长期投资',
    desc: '希望配置有利于长期投资的产品'
  },
  {
    value: 'stable',
    title: '稳健投资',
    desc: '希望配置风险较低的稳健产品'
  },
  {
    value: 'growth',
    title: '成长投资',
    desc: '希望配置有成长潜力的产品'
  },
  {
    value: 'balanced',
    title: '均衡投资',
    desc: '希望配置风险收益均衡的产品'
  }
])

// 选择投资时长
const selectDuration = (value) => {
  selectedDuration.value = value
}

// 选择收益率
const selectReturn = (value) => {
  selectedReturn.value = value
}

// 选择亏损承受
const selectLoss = (value) => {
  selectedLoss.value = value
}

// 返回上一页
const onBack = () => {
  router.back()
}

// 取消
const onCancel = () => {
  router.back()
}

// 显示投资偏好弹窗
const showPreferencePopup = () => {
  showPreference.value = true
}

// 选择投资偏好
const selectPreference = (value) => {
  selectedPreference.value = value
}

// 确认投资偏好
const confirmPreference = () => {
  showPreference.value = false
}

// 获取下一步按钮文本
const getNextButtonText = () => {
  const buttonTexts = {
    'target': '下一步',
    'suggestion': '下一步',
    'allocation': '下一步',
    'preview': '创建账本'
  }
  return buttonTexts[activeTab.value] || '下一步'
}

// 下一步
const onNext = () => {
  // 验证当前步骤的必填项
  if (activeTab.value === 'target') {
    if (!formData.value.accountName) {
      showToast('请输入账本名称')
      return
    }
    if (!selectedDuration.value) {
      showToast('请选择投资时长')
      return
    }
    if (!selectedReturn.value) {
      showToast('请选择理想收益率')
      return
    }
    if (!selectedLoss.value) {
      showToast('请选择可承受的最大亏损')
      return
    }
    activeTab.value = 'suggestion'
  } else if (activeTab.value === 'suggestion') {
    activeTab.value = 'allocation'
  } else if (activeTab.value === 'allocation') {
    activeTab.value = 'preview'
  } else {
    // 最后一步，提交数据
    console.log('提交心愿账本数据:', {
      ...formData.value,
      duration: selectedDuration.value,
      returnRate: selectedReturn.value,
      lossAcceptance: selectedLoss.value,
      preference: selectedPreference.value
    })
    showToast('心愿账本创建成功！')
    // 可以跳转到成功页面或返回账本列表
    setTimeout(() => {
      router.push('/')
    }, 1500)
  }
}
</script>

<style scoped>
.create-wish-account {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.custom-nav-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar .van-icon) {
  color: #fff;
}

.wish-tabs {
  background: #fff;
  margin-bottom: 2%;
}

:deep(.van-tabs__nav) {
  background: #fff;
}

:deep(.van-tab) {
  font-size: 14px;
}

:deep(.van-tabs__line) {
  background: #667eea;
}

.tab-content {
  padding: 0 4%;
}

.form-section {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4% 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item.clickable {
  cursor: pointer;
}

.form-item.clickable:hover {
  background: #f8f9fa;
}

.form-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-value {
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 2%;
}

.help-text {
  color: #667eea;
  font-size: 12px;
}

.preference-text {
  color: #333;
}

.custom-field {
  border: none;
  padding: 0;
  text-align: right;
}

:deep(.van-field__control) {
  text-align: right;
  color: #333;
}

.duration-options {
  display: flex;
  gap: 3%;
  margin: 4% 0;
}

.duration-option {
  flex: 1;
  padding: 3% 0;
  text-align: center;
  background: #f8f9fa;
  border-radius: 20px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.duration-option.active {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.return-options {
  display: flex;
  flex-wrap: wrap;
  gap: 3%;
  margin: 4% 0;
}

.return-option {
  width: 48%;
  padding: 4%;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 3%;
}

.return-option.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.return-rate {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2%;
}

.return-desc {
  font-size: 10px;
  color: #999;
  line-height: 1.4;
}

.loss-options {
  display: flex;
  gap: 3%;
  margin: 4% 0;
}

.loss-option {
  flex: 1;
  padding: 3% 0;
  text-align: center;
  background: #f8f9fa;
  border-radius: 20px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.loss-option.active {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.bottom-tip {
  text-align: center;
  font-size: 12px;
  color: #999;
  margin-top: 6%;
  padding-top: 4%;
  border-top: 1px solid #f0f0f0;
}

.placeholder {
  padding: 10%;
  text-align: center;
  color: #999;
  font-size: 14px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 4%;
  padding: 4%;
  background: #fff;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn {
  flex: 1;
  height: 44px;
  border: 1px solid #d9d9d9;
  background: #fff;
  color: #666;
  border-radius: 22px;
}

.next-btn {
  flex: 2;
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 22px;
  color: #fff;
  font-weight: 600;
}

/* 投资偏好弹窗样式 */
.preference-popup {
  border-radius: 16px 16px 0 0;
}

:deep(.van-popup) {
  max-height: 70vh;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4% 4% 2% 4%;
  border-bottom: 1px solid #f0f0f0;
}

.popup-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.popup-header .van-icon {
  font-size: 18px;
  color: #999;
  cursor: pointer;
}

.preference-options {
  padding: 4%;
  max-height: 50vh;
  overflow-y: auto;
}

.preference-option {
  padding: 4%;
  margin-bottom: 3%;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preference-option.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.preference-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2%;
}

.preference-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.popup-actions {
  padding: 4%;
  border-top: 1px solid #f0f0f0;
}

.popup-actions .van-button {
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
}
</style>
