<template>
  <div class="create-wish-account" ref="scrollContainer">
    <!-- 顶部导航 -->
    <van-nav-bar
      title="创建王维的心愿账本"
      left-arrow
      @click-left="onBack"
      class="custom-nav-bar"
    >
      <template #right>
        <van-icon name="ellipsis" />
        <van-icon name="question-o" style="margin-left: 8px;" />
      </template>
    </van-nav-bar>

    <!-- 进度条 -->
    <div class="progress-container">
      <div class="progress-steps">
        <div
          v-for="(step, index) in progressSteps"
          :key="index"
          :class="['progress-step', { active: index <= currentStepIndex }]"
        >
          <div class="step-circle">{{ index + 1 }}</div>
          <div class="step-label">{{ step.label }}</div>
        </div>
      </div>
      <div class="progress-line">
        <div
          class="progress-fill"
          :style="{ width: progressPercentage + '%' }"
        ></div>
      </div>
    </div>

    <!-- 投资数据目标页面 -->
    <div v-if="activeTab === 'target'" class="tab-content">
      <div class="form-section">
        <!-- 客户姓名 -->
        <div class="form-item">
          <label class="form-label">客户姓名</label>
          <div class="form-value">王维</div>
        </div>

        <!-- 账本名称 -->
        <div class="form-item">
          <label class="form-label" style="min-width: 80px;text-align: left;">账本名称</label>
          <van-field
            v-model="formData.accountName"
            placeholder="请输入"
            class="custom-field"
          />
        </div></div>
         <div style="text-align: left;margin-top:8px;margin-bottom: 8px;">
          <label class="form-label">投资目标</label>
          
        </div>
   <div class="form-section">
        <!-- 投资目标 -->
       

        <!-- 计划投资金额 -->
        <div class="form-item">
          <label class="form-label"  style="min-width: 150px;text-align: left;">计划投资金额(万元)</label>
          <van-field
            v-model="formData.plannedAmount"
            placeholder="请输入"
            class="custom-field"
          />
        </div>

        <!-- 计划投资时长 -->
        <div class="form-item">
          <label class="form-label">计划投资时长</label>
          <div class="form-value">
            <span class="help-text"></span>
          </div>
          <br>
          
        </div>

        <!-- 投资时长选择 -->
        <div class="duration-options">
          <div
            v-for="duration in durationOptions"
            :key="duration.value"
            :class="['duration-option', { active: selectedDuration === duration.value }]"
            @click="selectDuration(duration.value)"
          >
            {{ duration.label }}
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="divider-line"></div>

        <!-- 对这笔钱的理想年化收益 -->
        <div class="form-item">
          <label class="form-label">对这笔钱的理想年化收益</label>
        </div>
         <!-- 收益率选择 -->
        <div class="risk-options">
          <div
            v-for="returnRisk in RiskOptions"
            :key="returnRisk.value"
            :class="['risk-option', { active: selectedRisk === returnRisk.value }]"
            @click="selectRisk(returnRisk.value)"
          >
            <div class="risk-rate">{{ returnRisk.rate }}</div>
            <div class="risk-desc">{{ returnRisk.desc }}</div>
          </div>
        </div>

   <div class="form-item">
          <label class="form-label">可承受最大亏损</label>
        </div>
        <!-- 收益率选择 -->
        <div class="return-options">
          <div
            v-for="returnRate in returnOptions"
            :key="returnRate.value"
            :class="['return-option', { active: selectedReturn === returnRate.value }]"
            @click="selectReturn(returnRate.value)"
          >
            <div class="return-rate">{{ returnRate.rate }}</div>
            <div class="return-desc">{{ returnRate.desc }}</div>
          </div>
        </div>

        <!-- 投资偏好 -->
        <div class="form-item clickable" @click="showPreferencePopup">
          <label class="form-label">投资偏好</label>
          <div class="form-value">
            <span class="preference-text">希望配置利于长期投资的产品</span>
            <van-icon name="arrow" />
          </div>
        </div>

      
      </div>
    </div>

    <!-- 方案建议页面 -->
    <div v-if="activeTab === 'suggestion'" class="tab-content">
      <div v-if="loading" class="loading-container">
        <van-loading type="spinner" color="#667eea">分析中...</van-loading>
      </div>

      <div v-else class="suggestion-content">
        <!-- 标准配置策略介绍卡片 -->
        <div class="strategy-intro-card">
          <div class="intro-header">标准配置策略介绍</div>
        </div>

        <!-- 大类资产比例卡片 -->
        <div class="asset-allocation-card">
          <h4 class="card-title">大类资产比例</h4>
          <p class="allocation-desc">
            基于您的风险偏好、年龄以及可承受的风险等级，适合您的
            资产配置策略为：以配置稳健收益资产为主，权益资产在市场
            估值相对合理时可适当配置，参考资产配置比例如下：
          </p>

          <!-- 市场适度标签 -->
          <div class="market-level">
            <span class="level-label">市场适度</span>
          </div>

          <!-- 风险适度表格 -->
          <div class="risk-table">
            <div class="table-header">
              <div class="header-cell-empty"></div>
              <div class="header-cell">保守资产占比</div>
              <div class="header-cell">风险资产占比</div>
            </div>
            <div class="table-row">
              <div class="risk-level">低</div>
              <div class="conservative-ratio">20%</div>
              <div class="risk-ratio">80%</div>
            </div>
            <div class="table-row">
              <div class="risk-level">中</div>
              <div class="conservative-ratio">10%</div>
              <div class="risk-ratio">90%</div>
            </div>
            <div class="table-row">
              <div class="risk-level">高</div>
              <div class="conservative-ratio">0</div>
              <div class="risk-ratio">100%</div>
            </div>
          </div>

          <p class="risk-note">
            市场适度：参考基准是基准利率指数
          </p>
        </div>

        <!-- 保守型标准策略回测表现卡片 -->
        <div class="backtest-card">
          <h4 class="card-title">保守型标准策略回测表现</h4>
          <div class="backtest-period">{{ backtestData.period }}</div>

          <!-- 回测曲线图 -->
          <div class="chart-container">
            <canvas ref="backtestChart" width="350" height="200" class="backtest-chart"></canvas>
          </div>

          <!-- 回测数据 -->
          <div class="backtest-data">
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">年化收益：</span>
                <span class="data-value positive">{{ backtestData.annualReturn }}%</span>
              </div>
              <div class="data-item">
                <span class="data-label">基准收益：</span>
                <span class="data-value negative">{{ backtestData.benchmarkReturn }}%</span>
              </div>
            </div>
            <div class="data-row">
              <div class="data-item">
                <span class="data-label">年化最大回撤：</span>
                <span class="data-value negative">{{ backtestData.maxDrawdown }}%</span>
              </div>
              <div class="data-item">
                <span class="data-label">基准最大回撤：</span>
                <span class="data-value negative">{{ backtestData.benchmarkDrawdown }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 保守型标准策略滚动收益表现卡片 -->
        <div class="rolling-return-card">
          <h4 class="card-title">保守型标准策略滚动收益表现</h4>

          <!-- 图例 -->
          <div class="legend">
            <div class="legend-item">
              <div class="legend-dot blue"></div>
              <span>标准策略</span>
            </div>
            <div class="legend-item">
              <div class="legend-dot orange"></div>
              <span>平均收益基准</span>
            </div>
          </div>

          <!-- 柱状图 -->
          <div class="chart-container">
            <canvas ref="comparisonChart" width="350" height="200" class="comparison-chart"></canvas>
          </div>

          <!-- 数据说明 -->
          <p class="improvement-note">
            历史数据显示，平均收益基准表现会受到市场
            因素影响较大，中长期表现相对稳健
          </p>
        </div>
      </div>
    </div>

    <!-- 配置资产页面 -->
    <div v-if="activeTab === 'allocation'" class="tab-content">
      <div v-if="loadingAllocation" class="loading-container">
        <van-loading type="spinner" color="#667eea">加载中...</van-loading>
      </div>

      <!-- 策略匹配页面 -->
      <div v-else-if="hasStrategyMatch" class="strategy-match-content">
        <!-- 策略提示 -->
        <div class="strategy-tip">
          以下是自动匹配的基金投顾策略
        </div>

        <!-- 全球精选组合卡片 -->
        <div class="strategy-card">
          <div class="strategy-header">
            <h3 class="strategy-title">全球精选组合</h3>
            <div class="strategy-actions">
              <span class="view-customer">分享给客户</span>
            </div>
          </div>

          <div class="strategy-stats">
            <div class="stat-item">
              <div class="stat-label">中风险</div>
              <div class="stat-value">20.22%</div>
              <div class="stat-desc">成立以来收益</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">2年以上</div>
              <div class="stat-value">建议持有</div>
            </div>
          </div>

          <div class="strategy-details">
            <div class="details-header">
              <span class="details-title">策略理念</span>
              <span class="details-link">详细解读 ></span>
            </div>
            <p class="details-content">
              本组合投资于设置型基金、混合型基金、债券型基金、货
              币型基金和QDII型基金，其中QDII型基金比例不低于
              30%，通过全球化的资产配置...
            </p>
          </div>

          <!-- 策略图表占位 -->
          <div class="strategy-chart-placeholder">
            <div class="chart-item"></div>
            <div class="chart-item"></div>
            <div class="chart-item"></div>
            <div class="chart-item"></div>
            <div class="chart-item"></div>
            <div class="chart-item"></div>
            <div class="chart-item"></div>
            <div class="chart-item"></div>
          </div>
        </div>

        <!-- 测试按钮 -->
        <div class="test-buttons">
          <van-button
            type="default"
            size="small"
            @click="toggleStrategyMatch"
            class="test-btn"
          >
            {{ hasStrategyMatch ? '切换到基金列表' : '切换到策略匹配' }}
          </van-button>
          <van-button
            type="primary"
            size="small"
            @click="refreshStrategy"
            class="test-btn"
          >
            重新获取策略
          </van-button>
        </div>
      </div>

      <!-- 原基金配置页面 -->
      <div v-else class="allocation-content">
        <!-- 基金组合推荐 -->
        <div class="fund-combo-card">
          <div class="combo-header">
            <h3 class="combo-title">{{ fundCombo.name }}</h3>
            <span class="combo-count">({{ fundCombo.count }}只基金)</span>
            <div class="combo-actions">
              <span class="view-details">查看详情</span>
            </div>
          </div>
          <p class="combo-desc">{{ fundCombo.description }}</p>
          <van-button type="primary" class="select-combo-btn" @click="selectFundCombo">
            一键配置
          </van-button>
        </div>

        <!-- 资产配置清单 -->
        <div class="asset-list-card">
          <div class="list-header">
            <h3 class="list-title">资产配置清单</h3>
            <div class="list-actions">
              <span class="add-fund" @click="showFundSelector">添加基金</span>
            </div>
          </div>

          <!-- 权益类基金 -->
          <div class="fund-category">
            <div class="category-header" @click="toggleCategory('equity')">
              <van-icon name="arrow-down" :class="{ rotated: !equityCollapsed }" />
              <span class="category-name">权益类</span>
              <span class="category-ratio">占比{{ equityRatio }}%</span>
            </div>

            <div v-show="!equityCollapsed" class="fund-list">
              <div
                v-for="fund in equityFunds"
                :key="fund.id"
                class="fund-item"
              >
                <div class="fund-info">
                  <div class="fund-name">{{ fund.name }}</div>
                  <div class="fund-code">{{ fund.code }}</div>
                  <div class="fund-manager">{{ fund.manager }}</div>
                </div>
                <div class="fund-amount">
                  <div class="amount-value">{{ fund.amount }}</div>
                  <div class="amount-change" :class="fund.changeClass">{{ fund.change }}</div>
                </div>
                <van-icon name="edit" class="edit-icon" @click="editFund(fund)" />
              </div>

              <!-- 空位占位符 -->
              <div
                v-for="n in equityEmptySlots"
                :key="'equity-empty-' + n"
                class="fund-item empty"
                @click="addFund('equity')"
              >
                <div class="empty-content">
                  <van-icon name="plus" />
                  <span>添加基金</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 固收类基金 -->
          <div class="fund-category">
            <div class="category-header" @click="toggleCategory('fixed')">
              <van-icon name="arrow-down" :class="{ rotated: !fixedCollapsed }" />
              <span class="category-name">固收类</span>
              <span class="category-ratio">占比{{ fixedRatio }}%</span>
            </div>

            <div v-show="!fixedCollapsed" class="fund-list">
              <div
                v-for="fund in fixedFunds"
                :key="fund.id"
                class="fund-item"
              >
                <div class="fund-info">
                  <div class="fund-name">{{ fund.name }}</div>
                  <div class="fund-code">{{ fund.code }}</div>
                  <!-- <div class="fund-manager">{{ fund.manager }}</div> -->
                </div>
                <div class="fund-amount">
                  <div class="amount-value">{{ fund.amount }}</div>
                  <div class="amount-change" :class="fund.changeClass">{{ fund.change }}</div>
                </div>
                <van-icon name="edit" class="edit-icon" @click="editFund(fund)" />
              </div>

              <!-- 空位占位符 -->
              <div
                v-for="n in fixedEmptySlots"
                :key="'fixed-empty-' + n"
                class="fund-item empty"
                @click="addFund('fixed')"
              >
                <div class="empty-content">
                  <van-icon name="plus" />
                  <span>添加基金</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览完成页面 -->
    <div v-if="activeTab === 'preview'" class="tab-content">
      <div v-if="loadingPreview" class="preview-skeleton">
        <!-- 骨架屏 -->
        <div class="skeleton-header">
          <van-skeleton title :row="0" />
        </div>

        <div class="skeleton-card">
          <van-skeleton title :row="2" />
        </div>

        <div class="skeleton-card">
          <van-skeleton title :row="3" />
        </div>

        <div class="skeleton-card">
          <van-skeleton title :row="4" />
        </div>
      </div>

      <div v-else class="preview-content">
        <!-- 子女储备金投资 标题 -->
        <div class="preview-header">
          <h2 class="account-title">{{ previewData.accountName }}</h2>
        </div>

        <!-- 投资概览卡片 -->
        <div class="investment-overview-card">
          <div class="overview-stats">
            <div class="stat-item">
              <div class="stat-label">计划投资金额</div>
              <div class="stat-value">{{ previewData.plannedAmount }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">基金数量</div>
              <div class="stat-value">{{ previewData.fundCount }}只</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">创建日期</div>
              <div class="stat-value">{{ previewData.createDate }}</div>
            </div>
          </div>

          <div class="overview-actions">
            <van-button
              type="primary"
              size="small"
              icon="add"
              class="action-btn"
              @click="addFunds"
            >
              添加基金
            </van-button>
            <van-button
              type="default"
              size="small"
              icon="description"
              class="action-btn"
              @click="generateReport"
            >
              生成策略报告
            </van-button>
            <van-button
              type="default"
              size="small"
              icon="share"
              class="action-btn"
              @click="shareReport"
            >
              清单报告
            </van-button>
          </div>
        </div>

        <!-- 客户投资目标 -->
        <div class="investment-target-card">
          <div class="card-header">
            <h3 class="card-title">客户投资目标</h3>
            <span class="modify-link" @click="modifyTarget">修改</span>
          </div>
          <div class="target-tags">
            <span
              v-for="tag in previewData.investmentTags"
              :key="tag"
              class="target-tag"
            >
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- 配置理念 -->
        <div class="strategy-concept-card">
          <div class="card-header">
            <h3 class="card-title">配置理念</h3>
            <span class="modify-link" @click="modifyStrategy">修改</span>
          </div>
          <p class="concept-text">{{ previewData.strategyDescription }}</p>
        </div>

        <!-- 基金列表 -->
        <div class="fund-list-card">
          <div class="card-header">
            <h3 class="card-title">基金列表</h3>
            <span class="modify-link" @click="modifyFunds">修改</span>
          </div>

          <!-- 权益型基金 -->
          <div class="fund-category-section">
            <div class="category-header">
              <van-icon name="arrow-down" :class="{ rotated: !previewEquityCollapsed }" />
              <span class="category-name">权益型</span>
              <span class="category-ratio">占比{{ previewData.equityRatio }}%</span>
            </div>

            <div v-show="!previewEquityCollapsed" class="fund-items">
              <div
                v-for="fund in previewData.equityFunds"
                :key="fund.id"
                class="preview-fund-item"
              >
                <div class="fund-basic-info">
                  <div class="fund-name">{{ fund.name }}</div>
                  <div class="fund-code">{{ fund.code }}</div>
                  <div class="fund-manager">{{ fund.manager }}</div>
                </div>
                <div class="fund-performance">
                  <div class="performance-value">{{ fund.performance }}%</div>
                  <div class="performance-amount">{{ fund.amount }}</div>
                </div>
              </div>

              <!-- 投资点评 */
              <div class="investment-comment">
                <div class="comment-title">投资点评</div>
                <p class="comment-text">{{ previewData.equityComment }}</p>
              </div>
            </div>
          </div>

          <!-- 固收型基金 -->
          <div class="fund-category-section">
            <div class="category-header">
              <van-icon name="arrow-down" :class="{ rotated: !previewFixedCollapsed }" />
              <span class="category-name">固收型</span>
              <span class="category-ratio">占比{{ previewData.fixedRatio }}%</span>
            </div>

            <div v-show="!previewFixedCollapsed" class="fund-items">
              <div
                v-for="fund in previewData.fixedFunds"
                :key="fund.id"
                class="preview-fund-item"
              >
                <div class="fund-basic-info">
                  <div class="fund-name">{{ fund.name }}</div>
                  <div class="fund-code">{{ fund.code }}</div>
                  <div class="fund-manager">{{ fund.manager }}</div>
                </div>
                <div class="fund-performance">
                  <div class="performance-value">{{ fund.performance }}%</div>
                  <div class="performance-amount">{{ fund.amount }}</div>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="previewData.fixedFunds.length === 0" class="empty-funds">
                <div class="empty-placeholder"></div>
                <div class="empty-placeholder"></div>
                <div class="empty-placeholder"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-actions">
      <van-button class="cancel-btn" @click="onCancel">取消</van-button>
      <van-button type="primary" class="next-btn" @click="onNext">
        {{ getNextButtonText() }}
      </van-button>
    </div>

    <!-- 投资偏好弹窗 -->
    <van-popup v-model:show="showPreference" position="bottom" class="preference-popup">
      <div class="popup-header">
        <h3>投资偏好</h3>
        <van-icon name="cross" @click="showPreference = false" />
      </div>
      <div class="preference-options">
        <div
          v-for="preference in preferenceOptions"
          :key="preference.value"
          :class="['preference-option', { active: selectedPreference === preference.value }]"
          @click="selectPreference(preference.value)"
        >
          <div class="preference-title">{{ preference.title }}</div>
          <div class="preference-desc">{{ preference.desc }}</div>
        </div>
      </div>
      <div class="popup-actions">
        <van-button type="primary" block @click="confirmPreference">确认</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, nextTick, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { submitInvestmentGoals } from '../api/wishAccount.js'

const router = useRouter()

// 当前活跃标签
const activeTab = ref('target')

// 进度条步骤
const progressSteps = ref([
  { label: '投资数据目标', value: 'target' },
  { label: '方案建议', value: 'suggestion' },
  { label: '配置资产', value: 'allocation' },
  { label: '预览开户', value: 'preview' }
])

// 当前步骤索引
const currentStepIndex = ref(0)

// 进度百分比
const progressPercentage = computed(() => {
  return (currentStepIndex.value / (progressSteps.value.length - 1)) * 100
})

// 表单数据
const formData = ref({
  accountName: '',
  plannedAmount: ''
})

// 选中的投资时长
const selectedDuration = ref('')

// 投资时长选项
const durationOptions = ref([
  { label: '6个月以内', value: '6months' },
  { label: '6个月-1年', value: '6m-1y' },
  { label: '1年-3年', value: '1y-3y' },
  { label: '3年以上', value: '3y+' }
])

// 选中的收益率
const selectedReturn = ref('')

// 风险选项
const RiskOptions = ref([
  { value: '2.4', rate: '理财年化2%-4%', desc: '' },
  { value: '5.8', rate: '稳健年化5%-8%', desc: '' },
  { value: '10', rate: '追求年化10%+', desc: '' },
])
const selectedRisk = ref('')
// 收益率选项
const returnOptions = ref([
  { value: 'conservative', rate: '3%左右', desc: '' },
  { value: 'moderate', rate: '5%左右', desc: '' },
  { value: 'balanced', rate: '12%左右', desc: '' },
  { value: 'aggressive', rate: '20%左右', desc: '' }
])



// 投资偏好弹窗
const showPreference = ref(false)
const selectedPreference = ref('long-term')

// 加载状态
const loading = ref(false)

// 图表引用
const backtestChart = ref(null)
const comparisonChart = ref(null)
const scrollContainer = ref(null)

// 方案建议数据
const suggestionData = ref(null)

// Mock回测数据
const backtestData = ref({
  period: '2018-06-30~2023-06-30',
  annualReturn: 3.54,
  maxDrawdown: -2.11,
  benchmarkReturn: -3.35,
  benchmarkDrawdown: -2.98
})

// Mock滚动收益数据
const rollingReturnData = ref({
  periods: ['持有6个月', '持有1年', '持有2年', '持有3年'],
  strategyValues: [12.11, 12.11, 15.11, 15.11],
  benchmarkValues: [99.44, 99.44, 100, 100]
})

// 配置资产页面数据
const loadingAllocation = ref(false)
const hasStrategyMatch = ref(false) // 是否有策略匹配

// 预览页面数据
const loadingPreview = ref(false)
const previewEquityCollapsed = ref(false)
const previewFixedCollapsed = ref(false)

const previewData = ref({
  accountName: '子女储备金投资',
  plannedAmount: '100,000.00',
  fundCount: 5,
  createDate: '2023-08-01',
  investmentTags: ['计划储备 3年以上', '稳健理财 年化4%-6%', '基本策略 12%左右'],
  strategyDescription: '这里是关于配置理念的描述，这里是关于配置理念的描述，这里是关于配置理念的描述，这里是关于配置理念的描述。',
  equityRatio: 60,
  fixedRatio: 40,
  equityFunds: [
    {
      id: 1,
      name: '广发中证全指电力公用事业ETF',
      code: '000826',
      manager: '中泰资管',
      performance: 12.44,
      amount: '13,431.00'
    }
  ],
  fixedFunds: [],
  equityComment: '适度投资者可以考虑投资该基金的投资者，该基金主要投资于电力公用事业相关的股票，具有一定的投资价值和成长潜力。'
})

// 基金组合推荐
const fundCombo = ref({
  name: '8月基金组合',
  count: 10,
  description: '稳健+平衡策略，优选基金经理5年期方法配置公募基金，卫星配置TMT科技增强收益。'
})

// 分类折叠状态
const equityCollapsed = ref(false)
const fixedCollapsed = ref(false)

// 权益类基金
const equityFunds = ref([
  {
    id: 1,
    name: '广发中证全指电力公用事业ETF',
    code: '000826',
    manager: '中泰资管',
    amount: '20000.00',
    change: '121.33',
    changeClass: 'positive'
  }
])

// 固收类基金
const fixedFunds = ref([])

// 空位数量
const equityEmptySlots = ref(2)
const fixedEmptySlots = ref(3)

// 计算占比
const equityRatio = computed(() => {
  return equityFunds.value.length > 0 ? 60 : 0
})

const fixedRatio = computed(() => {
  return fixedFunds.value.length > 0 ? 40 : 0
})

// 投资偏好选项
const preferenceOptions = ref([
  {
    value: 'long-term',
    title: '长期投资',
    desc: '希望配置利于长期投资产品'
  },
  {
    value: 'stable',
    title: '稳健投资',
    desc: '希望配置风险较低稳健产品'
  },
  {
    value: 'growth',
    title: '成长投资',
    desc: '希望配置有成长潜力产品'
  },
  {
    value: 'balanced',
    title: '均衡投资',
    desc: '希望配置风险收益均衡产品'
  }
])

// 选择投资时长
const selectDuration = (value) => {
  selectedDuration.value = value
}

// 选择收益率
const selectReturn = (value) => {
  selectedReturn.value = value
}

// 选择收益率
const selectRisk = (value) => {
  selectedRisk.value = value
}


// 返回上一页
const onBack = () => {
  router.back()
}

// 取消
const onCancel = () => {
  router.back()
}

// 显示投资偏好弹窗
const showPreferencePopup = () => {
  showPreference.value = true
}

// 选择投资偏好
const selectPreference = (value) => {
  selectedPreference.value = value
}

// 确认投资偏好
const confirmPreference = () => {
  showPreference.value = false
}

// 配置资产页面方法
const selectFundCombo = () => {
  showToast('一键配置成功！')
  // 这里可以调用API配置基金组合
}

const toggleCategory = (category) => {
  if (category === 'equity') {
    equityCollapsed.value = !equityCollapsed.value
  } else if (category === 'fixed') {
    fixedCollapsed.value = !fixedCollapsed.value
  }
}

const addFund = (category) => {
  showToast(`添加${category === 'equity' ? '权益类' : '固收类'}基金`)
  // 这里可以打开基金选择器
}

const editFund = (fund) => {
  showToast(`编辑基金：${fund.name}`)
  // 这里可以打开基金编辑页面
}

const showFundSelector = () => {
  showToast('打开基金选择器')
  // 这里可以打开基金选择器弹窗
}

// 策略匹配相关方法
const checkStrategyMatch = async () => {
  try {
    // 模拟API调用检查是否有策略匹配
    // const response = await getStrategyMatch(userProfile)

    // 模拟随机结果，默认为false（没有策略匹配）
    const hasMatch = Math.random() > 0.7 // 30%概率有策略匹配

    console.log('策略匹配检查结果:', hasMatch)
    hasStrategyMatch.value = hasMatch

    return hasMatch
  } catch (error) {
    console.error('检查策略匹配失败:', error)
    hasStrategyMatch.value = false
    return false
  }
}

const toggleStrategyMatch = () => {
  hasStrategyMatch.value = !hasStrategyMatch.value
  showToast(hasStrategyMatch.value ? '切换到策略匹配页面' : '切换到基金列表页面')
}

const refreshStrategy = async () => {
  showToast('重新获取策略中...')
  loadingAllocation.value = true

  try {
    // 重新检查策略匹配
    await checkStrategyMatch()
    showToast(hasStrategyMatch.value ? '找到匹配策略！' : '未找到匹配策略')
  } finally {
    loadingAllocation.value = false
  }
}

// 预览页面方法
const loadPreviewData = async () => {
  loadingPreview.value = true

  try {
    // 模拟API调用获取预览数据
    // const response = await getPreviewData()

    // 模拟加载时间
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 使用Mock数据或API数据
    previewData.value = {
      accountName: formData.value.accountName || '子女储备金投资',
      plannedAmount: formData.value.plannedAmount ? `${formData.value.plannedAmount}.00` : '100,000.00',
      fundCount: 5,
      createDate: new Date().toISOString().split('T')[0],
      investmentTags: [
        '计划储备 3年以上',
        '稳健理财 年化4%-6%',
        '基本策略 12%左右'
      ],
      strategyDescription: '这里是关于配置理念的描述，这里是关于配置理念的描述，这里是关于配置理念的描述，这里是关于配置理念的描述。',
      equityRatio: 60,
      fixedRatio: 40,
      equityFunds: [
        {
          id: 1,
          name: '广发中证全指电力公用事业ETF',
          code: '000826',
          manager: '中泰资管',
          performance: 12.44,
          amount: '13,431.00'
        }
      ],
      fixedFunds: [],
      equityComment: '适度投资者可以考虑投资该基金的投资者，该基金主要投资于电力公用事业相关的股票，具有一定的投资价值和成长潜力。'
    }

    showToast('预览数据加载完成')

  } catch (error) {
    console.error('加载预览数据失败:', error)
    showToast('加载失败，使用模拟数据')
  } finally {
    loadingPreview.value = false
  }
}

// 预览页面交互方法
const addFunds = () => {
  showToast('添加基金')
}

const generateReport = () => {
  showToast('生成策略报告')
}

const shareReport = () => {
  showToast('分享清单报告')
}

const modifyTarget = () => {
  showToast('修改投资目标')
}

const modifyStrategy = () => {
  showToast('修改配置理念')
}

const modifyFunds = () => {
  showToast('修改基金列表')
}

// 加载配置资产数据
const loadAllocationData = async () => {
  loadingAllocation.value = true

  try {
    // 首先检查是否有策略匹配
    const hasMatch = await checkStrategyMatch()

    if (hasMatch) {
      // 有策略匹配，显示策略页面
      showToast('找到匹配的投顾策略！')
    } else {
      // 没有策略匹配，加载基金配置数据
      // 这里可以调用API获取基金组合和配置清单
      // const response = await getAllocationData()

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      // 使用Mock数据
      fundCombo.value = {
        name: '8月基金组合',
        count: 10,
        description: '稳健+平衡策略，优选基金经理5年期方法配置公募基金，卫星配置TMT科技增强收益。'
      }

      // 添加更多Mock基金数据
      equityFunds.value = [
        {
          id: 1,
          name: '广发中证全指电力公用事业ETF',
          code: '000826',
          manager: '中泰资管',
          amount: '20000.00',
          change: '121.33',
          changeClass: 'positive'
        }
      ]

      fixedFunds.value = []
      showToast('加载基金配置完成')
    }

  } catch (error) {
    console.error('加载配置资产数据失败:', error)
    showToast('加载失败，使用模拟数据')
    hasStrategyMatch.value = false
  } finally {
    loadingAllocation.value = false
  }
}

// 绘制回测曲线图
const drawBacktestChart = () => {
  if (!backtestChart.value) return

  const canvas = backtestChart.value
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 模拟回测数据点
  const strategyPoints = [
    { x: 50, y: 150 }, { x: 80, y: 140 }, { x: 110, y: 130 }, { x: 140, y: 125 },
    { x: 170, y: 120 }, { x: 200, y: 115 }, { x: 230, y: 110 }, { x: 260, y: 105 },
    { x: 290, y: 100 }, { x: 320, y: 95 }
  ]

  const benchmarkPoints = [
    { x: 50, y: 160 }, { x: 80, y: 155 }, { x: 110, y: 165 }, { x: 140, y: 170 },
    { x: 170, y: 175 }, { x: 200, y: 180 }, { x: 230, y: 185 }, { x: 260, y: 180 },
    { x: 290, y: 175 }, { x: 320, y: 170 }
  ]

  // 绘制网格线
  ctx.strokeStyle = '#f0f0f0'
  ctx.lineWidth = 1

  // 水平网格线
  for (let i = 0; i <= 4; i++) {
    const y = (height / 4) * i
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()
  }

  // 绘制曲线的函数
  const drawCurve = (points, color) => {
    if (points.length < 2) return

    ctx.strokeStyle = color
    ctx.lineWidth = 2
    ctx.beginPath()

    ctx.moveTo(points[0].x, points[0].y)

    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].x, points[i].y)
    }

    ctx.stroke()
  }

  // 绘制两条曲线
  drawCurve(strategyPoints, '#1890ff')  // 标准策略 - 蓝色
  drawCurve(benchmarkPoints, '#ff4d4f') // 基准 - 红色
}

// 绘制对比柱状图
const drawComparisonChart = () => {
  if (!comparisonChart.value) return

  const canvas = comparisonChart.value
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 柱状图数据
  const periods = ['持有6个月', '持有1年', '持有2年', '持有3年']
  const strategyData = [12.11, 12.11, 15.11, 15.11]
  const benchmarkData = [99.44, 99.44, 100, 100]

  const barWidth = 30
  const groupWidth = 80
  const startX = 40
  const maxValue = 120

  // 绘制柱状图
  periods.forEach((period, index) => {
    const x = startX + index * groupWidth

    // 标准策略柱子 (蓝色)
    const strategyHeight = (strategyData[index] / maxValue) * (height - 60)
    ctx.fillStyle = '#1890ff'
    ctx.fillRect(x, height - 40 - strategyHeight, barWidth, strategyHeight)

    // 平均收益基准柱子 (黄色)
    const benchmarkHeight = (benchmarkData[index] / maxValue) * (height - 60)
    ctx.fillStyle = '#faad14'
    ctx.fillRect(x + barWidth + 5, height - 40 - benchmarkHeight, barWidth, benchmarkHeight)

    // 绘制数值标签
    ctx.fillStyle = '#333'
    ctx.font = '10px Arial'
    ctx.textAlign = 'center'

    // 标准策略数值
    ctx.fillText(strategyData[index].toString(), x + barWidth/2, height - 45 - strategyHeight)

    // 基准数值
    ctx.fillText(benchmarkData[index].toString(), x + barWidth + 5 + barWidth/2, height - 45 - benchmarkHeight)

    // 期间标签
    ctx.fillText(period, x + barWidth + 2.5, height - 10)
  })
}

// 提交投资目标数据到后端
const submitInvestmentData = async () => {
  loading.value = true

  try {
    const requestData = {
      customerName: '王维',
      accountName: formData.value.accountName,
      plannedAmount: formData.value.plannedAmount,
      duration: selectedDuration.value,
      expectedReturn: selectedReturn.value,

      investmentPreference: selectedPreference.value
    }

    console.log('提交投资数据:', requestData)

    // 调用API获取方案建议
    const response = await submitInvestmentGoals(requestData)

    if (response.success) {
      suggestionData.value = response.data

      // 绘制图表
      setTimeout(() => {
        drawBacktestChart()
        drawComparisonChart()
      }, 100)

      showToast('方案分析完成！')
    } else {
      throw new Error(response.message || '获取方案建议失败')
    }

  } catch (error) {
    console.error('API调用失败，使用Mock数据:', error)

    // 使用Mock数据
    suggestionData.value = {
      strategy: 'conservative',
      assetAllocation: {
        conservative: { low: 20, medium: 10, high: 0 },
        risk: { low: 80, medium: 90, high: 100 }
      }
    }

    // 绘制图表
    setTimeout(() => {
      drawBacktestChart()
      drawComparisonChart()
    }, 100)

    showToast('使用模拟数据展示方案')
  } finally {
    loading.value = false
  }
}

// 获取下一步按钮文本
const getNextButtonText = () => {
  if (activeTab.value === 'allocation' && hasStrategyMatch.value) {
    return '下一步：手动配置'
  }

  const buttonTexts = {
    'target': '下一步',
    'suggestion': '下一步',
    'allocation': '下一步',
    'preview': '创建账本'
  }
  return buttonTexts[activeTab.value] || '下一步'
}

// 下一步
const onNext = async () => {
  // 验证当前步骤的必填项
  if (activeTab.value === 'target') {
    if (!formData.value.accountName) {
      showToast('请输入账本名称')
      return
    }
    if (!selectedDuration.value) {
      showToast('请选择投资时长')
      return
    }
    if (!selectedReturn.value) {
      showToast('请选择理想收益率')
      return
    }

    // 切换到方案建议页面并提交数据
    activeTab.value = 'suggestion'
    currentStepIndex.value = 1
    await submitInvestmentData()

  } else if (activeTab.value === 'suggestion') {
    activeTab.value = 'allocation'
    currentStepIndex.value = 2
    await loadAllocationData()
  } else if (activeTab.value === 'allocation') {
    activeTab.value = 'preview'
    currentStepIndex.value = 3
    await loadPreviewData()
  } else {
    // 最后一步，提交数据
    console.log('提交心愿账本数据:', {
      ...formData.value,
      duration: selectedDuration.value,
      returnRate: selectedReturn.value,

      preference: selectedPreference.value
    })
    showToast('心愿账本创建成功！')
    // 可以跳转到成功页面或返回账本列表
    setTimeout(() => {
      router.push('/')
    }, 1500)
  }
}

// 页面挂载后确保滚动正常
onMounted(() => {
  // 确保页面可以滚动
  if (scrollContainer.value) {
    scrollContainer.value.style.overflowY = 'auto'
    scrollContainer.value.style.height = '100vh'
  }

  // 监听窗口大小变化
  const handleResize = () => {
    if (scrollContainer.value) {
      scrollContainer.value.style.height = window.innerHeight + 'px'
    }
  }

  window.addEventListener('resize', handleResize)
  handleResize()
})
</script>

<style scoped>
/* 全局滚动修复 */
html, body {
  overflow-x: hidden;
  height: 100%;
}

* {
  box-sizing: border-box;
}

/* 确保移动端滚动正常 */
.create-wish-account {
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;
}
.create-wish-account {
  height: 100vh;
  background: #f5f5f5;
  padding-bottom: 100px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.custom-nav-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar .van-icon) {
  color: #fff;
}

/* 进度条样式 */
.progress-container {
  background: #fff;
  padding: 4% 4% 2% 4%;
  margin-bottom: 2%;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 3%;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.step-circle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 2%;
  transition: all 0.3s ease;
}

.progress-step.active .step-circle {
  background: #667eea;
  color: #fff;
}

.step-label {
  font-size: 10px;
  color: #999;
  text-align: center;
  transition: all 0.3s ease;
}

.progress-step.active .step-label {
  color: #667eea;
  font-weight: 600;
}

.progress-line {
  height: 2px;
  background: #f0f0f0;
  border-radius: 1px;
  position: relative;
  margin: 0 12%;
}

.progress-fill {
  height: 100%;
  background: #667eea;
  border-radius: 1px;
  transition: width 0.3s ease;
}

.tab-content {
  padding: 0 4%;
  min-height: calc(100vh - 200px);
  overflow-y: auto;
}

.form-section {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2% 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item.clickable {
  cursor: pointer;
}

.form-item.clickable:hover {
  background: #f8f9fa;
}

.form-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-value {
  font-size: 14px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 2%;
}

.help-text {
  color: #667eea;
  font-size: 12px;
}

.preference-text {
  color: #333;
}

.goal-title {
  color: #333;
  font-weight: 500;
}

.custom-field {
  border: none;
  padding: 0;
  text-align: right;
}

:deep(.van-field__control) {
  text-align: right;
  color: #333;
}

.duration-options {
  display: flex;
  gap: 2%;
  margin: 2% 0;
}

.duration-option {
  flex: 1;
  padding: 2.5% 1%;
  text-align: center;
  background: #f8f9fa;
  border-radius: 15px;
  font-size: 11px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.duration-option.active {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.divider-line {
  height: 1px;
  background: #f0f0f0;
  margin: 2% 0 2% 0;
}

.risk-options {
  display: flex;
  flex-wrap: wrap;
  gap: 3%;
  margin: 2% 0;
}

.risk-option {
  flex: 1;
  padding: 2.5% 1%;
  text-align: center;
  background: #f8f9fa;
  border-radius: 15px;
  font-size: 11px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.risk-option.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}
.risk-rate {
  font-size: 12px;
  /* font-weight/: 600/; */
  color: #333;
  margin-bottom: 2%;
}

.risk-desc {
  font-size: 10px;
  color: #999;
  line-height: 1.4;
}

.return-options {
  display: flex;
  flex-wrap: wrap;
  gap: 3%;
  margin: 2% 0;
}

.return-option {
  flex: 1;
  padding: 2.5% 1%;
  text-align: center;
  background: #f8f9fa;
  border-radius: 15px;
  font-size: 11px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.return-option.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.return-rate {
  font-size: 12px;
  /* font-weight: 600; */
  color: #333;
  margin-bottom: 2%;
}

.return-desc {
  font-size: 10px;
  color: #999;
  line-height: 1.4;
}



.bottom-tip {
  text-align: center;
  font-size: 12px;
  color: #999;
  margin-top: 6%;
  padding-top: 4%;
  border-top: 1px solid #f0f0f0;
}

.placeholder {
  padding: 10%;
  text-align: center;
  color: #999;
  font-size: 14px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 4%;
  padding: 4%;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.cancel-btn {
  flex: 1;
  height: 44px;
  border: 1px solid #d9d9d9;
  background: #fff;
  color: #666;
  border-radius: 22px;
}

.next-btn {
  flex: 2;
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 22px;
  color: #fff;
  font-weight: 600;
}

/* 投资偏好弹窗样式 */
.preference-popup {
  border-radius: 16px 16px 0 0;
}

:deep(.van-popup) {
  max-height: 70vh;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4% 4% 2% 4%;
  border-bottom: 1px solid #f0f0f0;
}

.popup-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.popup-header .van-icon {
  font-size: 18px;
  color: #999;
  cursor: pointer;
}

.preference-options {
  padding: 4%;
  max-height: 50vh;
  overflow-y: auto;
}

.preference-option {
  padding: 4%;
  margin-bottom: 3%;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.preference-option.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.preference-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2%;
}

.preference-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.popup-actions {
  padding: 4%;
  border-top: 1px solid #f0f0f0;
}

.popup-actions .van-button {
  height: 44px;
  border-radius: 22px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
}

/* 方案建议页面样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.suggestion-content {
  padding-bottom: 20%;
  min-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 策略介绍卡片 */
.strategy-intro-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 3%;
  padding: 4%;
}

.intro-header {
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  background: #f8f9fa;
  padding: 3% 0;
  border-radius: 8px;
}

/* 大类资产比例卡片 */
.asset-allocation-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 3%;
  padding: 4%;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 3% 0;
}

.allocation-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 4%;
}

.market-level {
  margin: 3% 0;
}

.level-label {
  background: #e6f7ff;
  color: #1890ff;
  padding: 1.5% 3%;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.risk-table {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  margin: 3% 0;
}

.table-header {
  display: flex;
  background: #fafafa;
  font-weight: 600;
  font-size: 12px;
}

.table-row {
  display: flex;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
}

.header-cell-empty {
  flex: 1;
  padding: 3% 2%;
}

.header-cell {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #333;
}

.risk-level {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #333;
  font-weight: 500;
}

.conservative-ratio {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #1890ff;
  font-weight: 600;
}

.risk-ratio {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #52c41a;
  font-weight: 600;
}

.risk-note {
  font-size: 11px;
  color: #999;
  margin: 3% 0 0 0;
}

/* 回测表现卡片 */
.backtest-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 3%;
  padding: 4%;
}

.backtest-period {
  font-size: 12px;
  color: #666;
  margin-bottom: 3%;
}

.chart-container {
  margin: 4% 0;
  text-align: center;
}

.backtest-chart,
.comparison-chart {
  width: 100%;
  height: auto;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.backtest-data {
  margin: 4% 0;
}

.data-row {
  display: flex;
  gap: 4%;
  margin-bottom: 2%;
}

.data-item {
  flex: 1;
  font-size: 12px;
}

.data-label {
  color: #666;
}

.data-value {
  font-weight: 600;
}

.data-value.positive {
  color: #52c41a;
}

.data-value.negative {
  color: #ff4d4f;
}

/* 滚动收益表现卡片 */
.rolling-return-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 20%;
  padding: 4%;
}

.legend {
  display: flex;
  gap: 6%;
  margin: 3% 0;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 2%;
  font-size: 12px;
  color: #666;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-dot.blue {
  background: #1890ff;
}

.legend-dot.orange {
  background: #faad14;
}

.improvement-note {
  font-size: 11px;
  color: #999;
  line-height: 1.5;
  margin-top: 3%;
  text-align: center;
}

.strategy-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 4%;
}

.strategy-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin: 0 0 4% 0;
  padding: 3% 0;
  background: #f8f9fa;
  border-radius: 8px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 4% 0 3% 0;
}

.allocation-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 4%;
}

.risk-table {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  margin: 3% 0;
}

.table-header {
  display: flex;
  background: #fafafa;
  font-weight: 600;
  font-size: 12px;
}

.table-row {
  display: flex;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
}

.header-cell {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #333;
}

.risk-level {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #333;
  font-weight: 500;
}

.conservative-ratio {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #1890ff;
  font-weight: 600;
}

.risk-ratio {
  flex: 1;
  padding: 3% 2%;
  text-align: center;
  color: #52c41a;
  font-weight: 600;
}

.risk-note {
  font-size: 11px;
  color: #999;
  margin: 3% 0;
}

.backtest-section {
  margin-top: 6%;
  padding-top: 4%;
  border-top: 1px solid #f0f0f0;
}

.backtest-period {
  font-size: 12px;
  color: #666;
  margin-bottom: 3%;
}

.chart-container {
  margin: 4% 0;
  text-align: center;
}

.backtest-chart,
.comparison-chart {
  width: 100%;
  height: auto;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.backtest-data {
  display: flex;
  flex-wrap: wrap;
  gap: 4%;
  margin: 4% 0;
}

.data-item {
  flex: 1;
  min-width: 45%;
  font-size: 12px;
  margin-bottom: 2%;
}

.data-label {
  color: #666;
}

.data-value {
  font-weight: 600;
}

.data-value.positive {
  color: #52c41a;
}

.data-value.negative {
  color: #ff4d4f;
}

.improvement-section {
  margin-top: 6%;
  padding-top: 4%;
  border-top: 1px solid #f0f0f0;
}

.legend {
  display: flex;
  gap: 6%;
  margin: 3% 0;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 2%;
  font-size: 12px;
  color: #666;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.improvement-note {
  font-size: 11px;
  color: #999;
  line-height: 1.5;
  margin-top: 3%;
  text-align: center;
}

/* 配置资产页面样式 */
.allocation-content {
  padding-bottom: 20%;
}

/* 策略匹配页面样式 */
.strategy-match-content {
  padding-bottom: 20%;
}

.strategy-tip {
  background: #f0f0f0;
  color: #666;
  font-size: 12px;
  padding: 3% 4%;
  margin-bottom: 3%;
  text-align: center;
}

.strategy-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 3%;
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4%;
}

.strategy-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.view-customer {
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
}

.strategy-stats {
  display: flex;
  gap: 8%;
  margin-bottom: 4%;
}

.stat-item {
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 1%;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 1%;
}

.stat-desc {
  font-size: 11px;
  color: #999;
}

.strategy-details {
  margin-bottom: 4%;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2%;
}

.details-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.details-link {
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
}

.details-content {
  font-size: 12px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.strategy-chart-placeholder {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2%;
  margin: 4% 0;
}

.chart-item {
  height: 60px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.test-buttons {
  display: flex;
  gap: 3%;
  margin: 4% 0;
  padding: 0 4%;
}

.test-btn {
  flex: 1;
  height: 36px;
  border-radius: 18px;
  font-size: 12px;
}

/* 预览页面样式 */
.preview-content {
  padding-bottom: 20%;
}

/* 骨架屏样式 */
.preview-skeleton {
  padding: 4%;
}

.skeleton-header {
  margin-bottom: 4%;
}

.skeleton-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 3%;
}

/* 预览页面标题 */
.preview-header {
  text-align: center;
  margin-bottom: 4%;
}

.account-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* 投资概览卡片 */
.investment-overview-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 3%;
}

.overview-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4%;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 1%;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.overview-actions {
  display: flex;
  gap: 3%;
}

.action-btn {
  flex: 1;
  height: 32px;
  border-radius: 16px;
  font-size: 11px;
}

/* 通用卡片样式 */
.investment-target-card,
.strategy-concept-card,
.fund-list-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 3%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3%;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.modify-link {
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
}

/* 投资目标标签 */
.target-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 2%;
}

.target-tag {
  background: #fff2e8;
  color: #fa8c16;
  padding: 1.5% 3%;
  border-radius: 12px;
  font-size: 11px;
  margin-bottom: 2%;
}

/* 配置理念 */
.concept-text {
  font-size: 12px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 基金分类 */
.fund-category-section {
  margin-bottom: 4%;
}

.fund-category-section:last-child {
  margin-bottom: 0;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 3% 0;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.category-header .van-icon {
  font-size: 14px;
  color: #666;
  margin-right: 2%;
  transition: transform 0.3s ease;
}

.category-header .van-icon.rotated {
  transform: rotate(-90deg);
}

.category-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.category-ratio {
  font-size: 12px;
  color: #1890ff;
  font-weight: 600;
}

/* 基金项目 */
.fund-items {
  padding: 2% 0;
}

.preview-fund-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3% 0;
  border-bottom: 1px solid #f8f9fa;
}

.preview-fund-item:last-child {
  border-bottom: none;
}

.fund-basic-info {
  flex: 1;
}

.fund-name {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  margin-bottom: 1%;
}

.fund-code {
  font-size: 11px;
  color: #999;
  margin-bottom: 1%;
}

.fund-manager {
  font-size: 11px;
  color: #666;
}

.fund-performance {
  text-align: right;
}

.performance-value {
  font-size: 13px;
  color: #52c41a;
  font-weight: 600;
  margin-bottom: 1%;
}

.performance-amount {
  font-size: 11px;
  color: #333;
}

/* 投资点评 */
.investment-comment {
  margin-top: 3%;
  padding-top: 3%;
  border-top: 1px solid #f0f0f0;
}

.comment-title {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2%;
}

.comment-text {
  font-size: 11px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 空状态 */
.empty-funds {
  display: flex;
  flex-direction: column;
  gap: 2%;
}

.empty-placeholder {
  height: 40px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

/* 基金组合推荐卡片 */
.fund-combo-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 3%;
  padding: 4%;
}

.combo-header {
  display: flex;
  align-items: center;
  margin-bottom: 3%;
}

.combo-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.combo-count {
  font-size: 12px;
  color: #666;
  margin-left: 2%;
}

.combo-actions {
  margin-left: auto;
}

.view-details {
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
}

.combo-desc {
  font-size: 12px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 4%;
}

.select-combo-btn {
  width: 100px;
  height: 32px;
  border-radius: 16px;
  background: #1890ff;
  border: none;
  font-size: 12px;
}

/* 资产配置清单卡片 */
.asset-list-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4%;
}

.list-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.add-fund {
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
}

/* 基金分类 */
.fund-category {
  margin-bottom: 4%;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 3% 0;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.category-header .van-icon {
  font-size: 14px;
  color: #666;
  margin-right: 2%;
  transition: transform 0.3s ease;
}

.category-header .van-icon.rotated {
  transform: rotate(-90deg);
}

.category-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.category-ratio {
  font-size: 12px;
  color: #1890ff;
  font-weight: 600;
}

/* 基金列表 */
.fund-list {
  padding: 2% 0;
}

.fund-item {
  display: flex;
  align-items: center;
  padding: 3% 0;
  border-bottom: 1px solid #f8f9fa;
}

.fund-item:last-child {
  border-bottom: none;
}

.fund-item.empty {
  background: #f8f9fa;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  margin-bottom: 2%;
  justify-content: center;
  cursor: pointer;
  min-height: 60px;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
  font-size: 12px;
}

.empty-content .van-icon {
  font-size: 20px;
  margin-bottom: 1%;
}

.fund-info {
  flex: 1;
}

.fund-name {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  margin-bottom: 1%;
}

.fund-code {
  font-size: 11px;
  color: #999;
  margin-bottom: 1%;
}

.fund-manager {
  font-size: 11px;
  color: #666;
}

.fund-amount {
  text-align: right;
  margin-right: 3%;
}

.amount-value {
  font-size: 13px;
  color: #333;
  font-weight: 600;
  margin-bottom: 1%;
}

.amount-change {
  font-size: 11px;
  font-weight: 600;
}

.amount-change.positive {
  color: #52c41a;
}

.amount-change.negative {
  color: #ff4d4f;
}

.edit-icon {
  font-size: 16px;
  color: #999;
  cursor: pointer;
}
</style>
