<template>
  <div class="select-type-page">
    <van-nav-bar
      title="选择账本类型"
      left-arrow
      @click-left="onBack"
      right-text="..."
      class="nav-bar"
    />
    
    <div class="page-content">
      <!-- 持仓账本选项 -->
      <div class="account-type-card holding-card">
        <div class="card-header">
          <div class="icon-wrapper holding-icon">
            <van-icon name="chart-trending-o" size="24" color="#ff6b6b" />
          </div>
          <h3 class="card-title">持仓账本</h3>
        </div>
        <p class="card-desc">根据金平台已持有资产创建账本</p>
        <van-button 
          class="create-btn holding-btn"
          block
          @click="createHoldingAccount"
        >
          立即创建
        </van-button>
      </div>
      
      <!-- 心愿账本选项 -->
      <div class="account-type-card wishlist-card">
        <div class="card-header">
          <div class="icon-wrapper wishlist-icon">
            <van-icon name="gold-coin-o" size="24" color="#ff9500" />
          </div>
          <h3 class="card-title">心愿账本</h3>
        </div>
        <p class="card-desc">根据您愿意承担风险及推荐精选创建账本</p>
        <van-button 
          class="create-btn wishlist-btn"
          block
          @click="createWishlistAccount"
        >
          立即创建
        </van-button>
      </div>
      
      <!-- 风险提示 -->
      <div class="risk-notice">
        <h4 class="notice-title">市场有风险，投资需谨慎</h4>
        <p class="notice-content">
          在进行基金投资前，请参阅基金的《基金合同》、《招募说明书》等法律文件，本功能不作为任何宣传材料。投资建议或保证，基金管理人承诺以诚实信用、勤勉尽责的原则管理和运作基金资产，但不保证基金一定盈利，也不保证最低收益。同时过往业绩不预示其未来表现，基金管理人管理的其他基金的业绩并不构成基金业绩表现的保证。投资者应当主要依据基金招募说明书作出投资决策，在作出投资决策后，基金运营状况与基金净值变化引致的投资风险，由投资者自行承担。投资者购买货币市场基金并不等同于将资金作为存款存放在银行或者存款类金融机构，基金管理人不保证基金一定盈利，也不保证最低收益。
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

const onBack = () => {
  router.back()
}

const createHoldingAccount = () => {
  router.push({ name: 'CreateHoldingAccount' })
}

const createWishlistAccount = () => {
  router.push({ name: 'CreateWishAccount' })
}
</script>

<style scoped>
/* 页面容器 */
.select-type-page {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.nav-bar {
  background: #ff6b6b !important;
  color: #fff;
  flex-shrink: 0;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 4% 4% 6% 4%;
  overflow-y: auto;
}

/* 账本类型卡片 */
.account-type-card {
  background: #fff;
  border-radius: 12px;
  padding: 6% 5%;
  margin-bottom: 4%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.account-type-card:active {
  transform: scale(0.98);
}

/* 持仓账本卡片 */
.holding-card {
  background: linear-gradient(135deg, #fff0f0 0%, #fff 100%);
  border: 1px solid #ffe0e0;
}

/* 心愿账本卡片 */
.wishlist-card {
  background: linear-gradient(135deg, #fff8e1 0%, #fff 100%);
  border: 1px solid #ffe0b3;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 4%;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4%;
}

.holding-icon {
  background: #fff0f0;
}

.wishlist-icon {
  background: #fff8e1;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* 卡片描述 */
.card-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 6% 0;
}

/* 创建按钮 */
.create-btn {
  height: 44px !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease;
}

.holding-btn {
  background: #ff6b6b !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.holding-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(255, 107, 107, 0.4);
}

.wishlist-btn {
  background: #ff9500 !important;
  border: none !important;
  color: #fff !important;
  box-shadow: 0 2px 8px rgba(255, 149, 0, 0.3);
}

.wishlist-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(255, 149, 0, 0.4);
}

/* 风险提示 */
.risk-notice {
  background: #fff;
  border-radius: 12px;
  padding: 5%;
  margin-top: 6%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.notice-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4% 0;
  text-align: center;
}

.notice-content {
  font-size: 12px;
  color: #666;
  line-height: 1.6;
  margin: 0;
  text-align: justify;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .page-content {
    padding: 5% 5% 8% 5%;
  }
  
  .account-type-card {
    padding: 7% 6%;
  }
  
  .card-title {
    font-size: 17px;
  }
  
  .card-desc {
    font-size: 13px;
  }
  
  .notice-content {
    font-size: 11px;
  }
}

@media (min-width: 768px) {
  .page-content {
    max-width: 600px;
    margin: 0 auto;
    padding: 3% 6% 4% 6%;
  }
  
  .account-type-card {
    padding: 5% 4%;
  }
}

/* 导航栏样式优化 */
:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__text) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}
</style>
