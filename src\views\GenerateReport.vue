<template>
  <div class="generate-report">
    <van-nav-bar
      title="生成资配报告"
      left-arrow
      @click-left="$router.back()"
      class="custom-nav-bar"
    >
      <template #right>
        <van-icon name="ellipsis" />
      </template>
    </van-nav-bar>
    
    <div class="report-content">
      <!-- 财富规划报告卡片 -->
      <div class="report-card">
        <div class="card-header">
          <div class="report-info">
            <h3 class="report-title">财富规划报告书</h3>
            <div class="report-meta">
              <span class="report-name">子女储备金投资</span>
              <span class="report-date">报告日期：2023-08-02</span>
            </div>
          </div>
          <div class="report-status">
            <span class="status-text">取消发布</span>
            <van-icon name="arrow" />
          </div>
        </div>
      </div>
      
      <!-- 报告详情卡片 -->
      <div class="report-details-card">
        <div class="detail-item" @click="editContent">
          <span class="detail-label">报告内容概览</span>
          <div class="detail-value">
            <span class="detail-text">{{ reportContent || '无内容' }}</span>
            <van-icon name="arrow" />
          </div>
        </div>

        <div class="detail-item" @click="editExpireDate">
          <span class="detail-label">报告外发链接生效期限</span>
          <div class="detail-value">
            <span class="detail-text">2023-09-01</span>
            <van-icon name="arrow" />
          </div>
        </div>
      </div>
      
      <!-- 发布按钮 -->
      <div class="publish-section">
        <van-button 
          type="primary" 
          block 
          round
          class="publish-btn"
          @click="publishReport"
        >
          正式发布
        </van-button>
        
        <p class="publish-note">
          发布后可生成分享链接和报告
        </p>
        
        <div class="last-report" @click="viewLastReport">
          <span class="last-report-text">上一次报告发布时间</span>
          <span class="last-report-date">2023-07-12</span>
          <van-icon name="arrow" />
        </div>
      </div>
      
      <!-- 报告预览区域 -->
      <div class="report-preview">
        <div v-if="!isPublished" class="preview-placeholder">
          <div class="placeholder-content">
            <van-icon name="description" class="placeholder-icon" />
            <p class="placeholder-text">报告预览将在发布后显示</p>
          </div>
        </div>

        <div v-else class="report-preview-content">
          <canvas ref="reportCanvas" width="350" height="500" class="report-canvas"></canvas>
        </div>
      </div>
    </div>
    
    <!-- 发布成功弹窗 -->
    <van-dialog
      v-model:show="showPublishDialog"
      title="发布成功"
      :show-cancel-button="false"
      confirm-button-text="查看报告"
      @confirm="viewReport"
    >
      <div class="publish-success-content">
        <van-icon name="success" class="success-icon" />
        <p>财富规划报告已成功发布！</p>
        <p>您可以分享链接给客户查看</p>
      </div>
    </van-dialog>
    
    <!-- 报告内容编辑弹窗 -->
    <van-popup
      v-model:show="showContentEditor"
      position="bottom"
      :style="{ height: '70%' }"
      round
    >
      <div class="content-editor">
        <div class="editor-header">
          <h3>编辑报告内容</h3>
          <van-icon name="cross" @click="showContentEditor = false" />
        </div>
        
        <div class="editor-body">
          <van-field
            v-model="reportContent"
            type="textarea"
            placeholder="请输入报告内容概览..."
            rows="10"
            autosize
          />
        </div>
        
        <div class="editor-footer">
          <van-button 
            type="primary" 
            block 
            @click="saveContent"
          >
            保存内容
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 弹窗状态
const showPublishDialog = ref(false)
const showContentEditor = ref(false)

// 报告状态
const isPublished = ref(false)

// 报告内容
const reportContent = ref('')

// 画布引用
const reportCanvas = ref(null)

// 发布报告
const publishReport = () => {
  showToast.loading({
    message: '正在发布报告...',
    forbidClick: true,
    duration: 2000
  })

  setTimeout(() => {
    isPublished.value = true
    showPublishDialog.value = true
    // 绘制报告预览
    setTimeout(() => {
      drawReportPreview()
    }, 100)
  }, 2000)
}

// 查看报告
const viewReport = () => {
  showPublishDialog.value = false
  showToast('跳转到报告详情页面')
  // 这里可以跳转到具体的报告详情页面
  // router.push('/report-detail')
}

// 编辑内容
const editContent = () => {
  showContentEditor.value = true
}

// 保存内容
const saveContent = () => {
  showContentEditor.value = false
  showToast('内容已保存')
}

// 编辑有效期
const editExpireDate = () => {
  showToast('编辑有效期功能')
}

// 查看上次报告
const viewLastReport = () => {
  showToast('查看上次报告')
}

// 绘制报告预览
const drawReportPreview = () => {
  if (!reportCanvas.value) return

  const canvas = reportCanvas.value
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 设置背景
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, width, height)

  // 绘制标题
  ctx.fillStyle = '#333333'
  ctx.font = 'bold 18px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('财富规划报告书', width / 2, 40)

  // 绘制副标题
  ctx.font = '14px Arial'
  ctx.fillStyle = '#666666'
  ctx.fillText('子女储备金投资', width / 2, 65)

  // 绘制分割线
  ctx.strokeStyle = '#e0e0e0'
  ctx.lineWidth = 1
  ctx.beginPath()
  ctx.moveTo(30, 85)
  ctx.lineTo(width - 30, 85)
  ctx.stroke()

  // 绘制投资概览
  ctx.fillStyle = '#333333'
  ctx.font = 'bold 16px Arial'
  ctx.textAlign = 'left'
  ctx.fillText('投资概览', 30, 115)

  // 投资金额
  ctx.font = '12px Arial'
  ctx.fillStyle = '#666666'
  ctx.fillText('计划投资金额：¥100,000.00', 30, 140)
  ctx.fillText('基金数量：5只', 30, 160)
  ctx.fillText('创建日期：2023-08-02', 30, 180)

  // 绘制资产配置图表
  ctx.fillStyle = '#333333'
  ctx.font = 'bold 16px Arial'
  ctx.fillText('资产配置', 30, 220)

  // 绘制饼图
  const centerX = width / 2
  const centerY = 280
  const radius = 50

  // 权益类 60%
  ctx.fillStyle = '#1890ff'
  ctx.beginPath()
  ctx.moveTo(centerX, centerY)
  ctx.arc(centerX, centerY, radius, -Math.PI / 2, -Math.PI / 2 + (Math.PI * 2 * 0.6))
  ctx.closePath()
  ctx.fill()

  // 固收类 40%
  ctx.fillStyle = '#52c41a'
  ctx.beginPath()
  ctx.moveTo(centerX, centerY)
  ctx.arc(centerX, centerY, radius, -Math.PI / 2 + (Math.PI * 2 * 0.6), -Math.PI / 2 + (Math.PI * 2))
  ctx.closePath()
  ctx.fill()

  // 图例
  ctx.fillStyle = '#1890ff'
  ctx.fillRect(30, 350, 12, 12)
  ctx.fillStyle = '#333333'
  ctx.font = '12px Arial'
  ctx.fillText('权益类 60%', 50, 361)

  ctx.fillStyle = '#52c41a'
  ctx.fillRect(30, 375, 12, 12)
  ctx.fillStyle = '#333333'
  ctx.fillText('固收类 40%', 50, 386)

  // 绘制基金列表
  ctx.fillStyle = '#333333'
  ctx.font = 'bold 16px Arial'
  ctx.fillText('基金列表', 30, 420)

  ctx.font = '12px Arial'
  ctx.fillStyle = '#666666'
  ctx.fillText('• 广发中证全指电力公用事业ETF', 30, 445)
  ctx.fillText('• 易方达稳健收益债券A', 30, 465)
  ctx.fillText('• 华夏纯债债券A', 30, 485)
}

onMounted(() => {
  // 页面加载时如果已发布则绘制预览
  if (isPublished.value) {
    setTimeout(() => {
      drawReportPreview()
    }, 100)
  }
})
</script>

<style scoped>
.generate-report {
  height: 100vh;
  background: #f5f5f5;
  overflow-y: auto;
}

.custom-nav-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar .van-icon) {
  color: #fff;
}

.report-content {
  padding: 4%;
  padding-bottom: 20%;
}

/* 报告卡片样式 */
.report-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 3%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.report-info {
  flex: 1;
}

.report-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 2% 0;
}

.report-meta {
  display: flex;
  flex-direction: column;
  gap: 1%;
}

.report-name {
  font-size: 14px;
  color: #666;
}

.report-date {
  font-size: 12px;
  color: #999;
}

.report-status {
  display: flex;
  align-items: center;
  gap: 1%;
  color: #1890ff;
  cursor: pointer;
}

.status-text {
  font-size: 12px;
}

/* 报告详情卡片 */
.report-details-card {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  margin-bottom: 4%;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3% 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 14px;
  color: #333;
}

.detail-value {
  display: flex;
  align-items: center;
  gap: 2%;
}

.detail-text {
  font-size: 14px;
  color: #666;
}

/* 发布区域 */
.publish-section {
  text-align: center;
  margin-bottom: 6%;
}

.publish-btn {
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  margin-bottom: 3%;
}

.publish-note {
  font-size: 12px;
  color: #999;
  margin: 0 0 4% 0;
}

.last-report {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2%;
  color: #1890ff;
  cursor: pointer;
}

.last-report-text {
  font-size: 12px;
}

.last-report-date {
  font-size: 12px;
  font-weight: 600;
}

/* 报告预览 */
.report-preview {
  background: #fff;
  border-radius: 12px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4%;
}

.preview-placeholder {
  text-align: center;
}

.placeholder-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 4%;
}

.placeholder-text {
  font-size: 14px;
  color: #999;
  margin: 0;
}

.report-preview-content {
  width: 100%;
  text-align: center;
}

.report-canvas {
  max-width: 100%;
  height: auto;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 发布成功弹窗 */
.publish-success-content {
  text-align: center;
  padding: 4%;
}

.success-icon {
  font-size: 48px;
  color: #52c41a;
  margin-bottom: 4%;
}

.publish-success-content p {
  font-size: 14px;
  color: #666;
  margin: 2% 0;
}

/* 内容编辑器 */
.content-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4%;
  border-bottom: 1px solid #f0f0f0;
}

.editor-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.editor-body {
  flex: 1;
  padding: 4%;
}

.editor-footer {
  padding: 4%;
  border-top: 1px solid #f0f0f0;
}
</style>
