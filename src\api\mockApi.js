/**
 * Mock API 服务
 * 模拟后端接口，提供基金数据和账本操作
 */

// Mock 持仓基金数据
const mockHoldingFunds = [
  {
    id: 'fund001',
    name: '易方达蓝筹精选混合',
    code: '005827',
    amount: '7,317.15',
    unitValue: '1.0330',
    totalValue: '8,708.42',
    dailyChange: '+0.12%',
    totalReturn: '+19.02%',
    riskLevel: '中风险',
    fundType: '混合型',
    fundManager: '张坤',
    establishDate: '2018-03-07'
  },
  {
    id: 'fund002', 
    name: '华夏成长混合',
    code: '000001',
    amount: '2,234.19',
    unitValue: '2.0685',
    totalValue: '4,621.42',
    dailyChange: '-0.08%',
    totalReturn: '+106.85%',
    riskLevel: '中高风险',
    fundType: '混合型',
    fundManager: '董阳阳',
    establishDate: '2013-08-24'
  },
  {
    id: 'fund003',
    name: '南方积极配置混合',
    code: '202005',
    amount: '1,376.81',
    unitValue: '1.6331',
    totalValue: '2,063.72',
    dailyChange: '+0.25%',
    totalReturn: '+63.31%',
    riskLevel: '中风险',
    fundType: '混合型',
    fundManager: '应帅',
    establishDate: '2004-09-29'
  },
  {
    id: 'fund004',
    name: '博时主题行业混合',
    code: '160505',
    amount: '759.15',
    unitValue: '1.0506',
    totalValue: '820.34',
    dailyChange: '+0.03%',
    totalReturn: '+5.06%',
    riskLevel: '中高风险',
    fundType: '混合型',
    fundManager: '肖瑞瑾',
    establishDate: '2005-01-06'
  },
  {
    id: 'fund005',
    name: '嘉实成长收益混合',
    code: '070001',
    amount: '341.93',
    unitValue: '1.0768',
    totalValue: '367.85',
    dailyChange: '-0.15%',
    totalReturn: '+7.68%',
    riskLevel: '中风险',
    fundType: '混合型',
    fundManager: '归凯',
    establishDate: '2003-07-09'
  }
]

// Mock 外部基金数据（支付宝等第三方平台）
const mockExternalFunds = [
  {
    id: 'ext001',
    name: '天弘余额宝货币',
    code: '000198',
    holdingAmount: '12,547.83',
    unitValue: '1.0000',
    totalProfit: '+547.83',
    dailyChange: '+0.01%',
    riskLevel: '低风险',
    fundType: '货币型',
    platform: '支付宝'
  },
  {
    id: 'ext002',
    name: '易方达蓝筹精选',
    code: '005827',
    holdingAmount: '5,234.19',
    unitValue: '2.0685',
    totalProfit: '+1,234.19',
    dailyChange: '-0.08%',
    riskLevel: '中高风险',
    fundType: '混合型',
    platform: '支付宝'
  },
  {
    id: 'ext003',
    name: '华夏成长混合',
    code: '000001',
    holdingAmount: '3,376.81',
    unitValue: '1.6331',
    totalProfit: '+376.81',
    dailyChange: '+0.25%',
    riskLevel: '中风险',
    fundType: '混合型',
    platform: '微信理财通'
  },
  {
    id: 'ext004',
    name: '南方积极配置',
    code: '202005',
    holdingAmount: '2,159.15',
    unitValue: '1.0506',
    totalProfit: '+159.15',
    dailyChange: '+0.03%',
    riskLevel: '中高风险',
    fundType: '混合型',
    platform: '天天基金'
  },
  {
    id: 'ext005',
    name: '博时主题行业',
    code: '160505',
    holdingAmount: '1,841.93',
    unitValue: '1.0768',
    totalProfit: '+41.93',
    dailyChange: '-0.15%',
    riskLevel: '中风险',
    fundType: '混合型',
    platform: '蚂蚁财富'
  }
]

// 模拟网络延迟
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

/**
 * 获取持仓基金列表
 * @returns {Promise<Object>} API响应
 */
export const getHoldingFunds = async () => {
  await delay()
  return {
    success: true,
    data: {
      funds: mockHoldingFunds,
      totalCount: mockHoldingFunds.length,
      totalValue: mockHoldingFunds.reduce((sum, fund) => 
        sum + parseFloat(fund.totalValue.replace(',', '')), 0
      ).toFixed(2),
      dataDate: '2024-06-01'
    },
    message: '获取持仓基金成功'
  }
}

/**
 * 获取外部基金列表
 * @returns {Promise<Object>} API响应
 */
export const getExternalFunds = async () => {
  await delay()
  return {
    success: true,
    data: {
      funds: mockExternalFunds,
      totalCount: mockExternalFunds.length,
      platforms: ['支付宝', '微信理财通', '天天基金', '蚂蚁财富']
    },
    message: '获取外部基金成功'
  }
}

/**
 * 搜索基金
 * @param {string} keyword - 搜索关键词
 * @returns {Promise<Object>} API响应
 */
export const searchFunds = async (keyword) => {
  await delay(300)
  
  const allFunds = [...mockHoldingFunds, ...mockExternalFunds]
  const filteredFunds = allFunds.filter(fund => 
    fund.name.includes(keyword) || 
    fund.code.includes(keyword)
  )
  
  return {
    success: true,
    data: {
      funds: filteredFunds,
      totalCount: filteredFunds.length,
      keyword
    },
    message: '搜索基金成功'
  }
}

/**
 * 创建投资账本
 * @param {Object} accountData - 账本数据
 * @returns {Promise<Object>} API响应
 */
export const createAccount = async (accountData) => {
  await delay(1000)
  
  // 模拟创建成功
  const newAccount = {
    id: `account_${Date.now()}`,
    ...accountData,
    createTime: new Date().toISOString(),
    status: 'active',
    totalValue: accountData.selected_products?.reduce((sum, fund) => 
      sum + parseFloat(fund.totalValue?.replace(',', '') || fund.holdingAmount?.replace(',', '') || 0), 0
    ).toFixed(2) || '0.00'
  }
  
  return {
    success: true,
    data: newAccount,
    message: '账本创建成功'
  }
}

/**
 * 获取账本详情
 * @param {string} accountId - 账本ID
 * @returns {Promise<Object>} API响应
 */
export const getAccountDetail = async (accountId) => {
  await delay()
  
  // 模拟账本详情数据
  const accountDetail = {
    id: accountId,
    name: '持仓账本#********',
    type: 'holding',
    createTime: '2024-07-10T10:30:00.000Z',
    totalValue: '16,581.75',
    totalProfit: '+2,581.75',
    profitRate: '+18.45%',
    investmentPeriod: '1-3years',
    funds: mockHoldingFunds.slice(0, 3),
    riskLevel: '中风险',
    status: 'active'
  }
  
  return {
    success: true,
    data: accountDetail,
    message: '获取账本详情成功'
  }
}

/**
 * 上传持仓截图并识别
 * @param {File} imageFile - 图片文件
 * @returns {Promise<Object>} API响应
 */
export const uploadHoldingImage = async (imageFile) => {
  await delay(2000) // 模拟OCR识别时间
  
  // 模拟OCR识别结果
  const recognizedFunds = mockExternalFunds.slice(0, 3).map(fund => ({
    ...fund,
    confidence: Math.random() * 0.3 + 0.7 // 识别置信度 70%-100%
  }))
  
  return {
    success: true,
    data: {
      recognizedFunds,
      imageUrl: URL.createObjectURL(imageFile),
      recognitionTime: new Date().toISOString()
    },
    message: 'OCR识别完成'
  }
}

/**
 * 获取基金详细信息
 * @param {string} fundCode - 基金代码
 * @returns {Promise<Object>} API响应
 */
export const getFundDetail = async (fundCode) => {
  await delay()
  
  const fund = [...mockHoldingFunds, ...mockExternalFunds]
    .find(f => f.code === fundCode)
  
  if (!fund) {
    return {
      success: false,
      message: '基金不存在'
    }
  }
  
  // 扩展基金详细信息
  const fundDetail = {
    ...fund,
    description: '该基金主要投资于具有良好成长性的优质企业股票',
    netValueHistory: [
      { date: '2024-07-10', value: fund.unitValue },
      { date: '2024-07-09', value: (parseFloat(fund.unitValue) - 0.01).toFixed(4) },
      { date: '2024-07-08', value: (parseFloat(fund.unitValue) - 0.02).toFixed(4) }
    ],
    holdings: [
      { stock: '贵州茅台', ratio: '8.5%' },
      { stock: '五粮液', ratio: '6.2%' },
      { stock: '招商银行', ratio: '5.8%' }
    ]
  }
  
  return {
    success: true,
    data: fundDetail,
    message: '获取基金详情成功'
  }
}

// 导出所有API方法
export default {
  getHoldingFunds,
  getExternalFunds,
  searchFunds,
  createAccount,
  getAccountDetail,
  uploadHoldingImage,
  getFundDetail
}
