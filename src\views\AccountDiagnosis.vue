<template>
  <div class="diagnosis-page">
    <van-nav-bar
      title="我的投资账本"
      left-arrow
      @click-left="onBack"
      class="nav-bar"
    />
    
    <div class="page-content">
      <!-- 账户诊断模块 -->
      <div class="diagnosis-section">
        <div class="section-header">
          <h2 class="section-title">账户诊断</h2>
          <p class="section-desc">
            上传持仓数据，快速识别客户投资偏好，即享5大特色服务，助您实现资产风险保障的投资意义。
          </p>
        </div>
        
        <div class="service-grid">
          <div class="service-item">
            <van-icon name="chart-trending-o" size="20" color="#ff6b6b" />
            <span>组合客群分析</span>
          </div>
          <div class="service-item">
            <van-icon name="gold-coin-o" size="20" color="#ff6b6b" />
            <span>组合回测</span>
          </div>
          <div class="service-item">
            <van-icon name="balance-o" size="20" color="#ff6b6b" />
            <span>持仓基金诊断</span>
          </div>
          <div class="service-item">
            <van-icon name="guide-o" size="20" color="#ff6b6b" />
            <span>资金指南</span>
          </div>
          <div class="service-item">
            <van-icon name="manager-o" size="20" color="#ff6b6b" />
            <span>全方位风险扫描</span>
          </div>
        </div>
      </div>
      
      <!-- 心愿账本模块 -->
      <div class="wishlist-section">
        <div class="section-header">
          <h2 class="section-title">心愿账本</h2>
          <p class="section-desc">
            华泰金融理财专家配置指南，孩子教育、养老理财，让您的财富在长期有保障
          </p>
        </div>
        
        <div class="wishlist-visual">
          <!-- 可视化图表区域 -->
          <div class="chart-container">
            <svg width="100%" height="200" viewBox="0 0 300 200" class="wishlist-chart">
              <!-- 背景渐变 -->
              <defs>
                <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#ff9500;stop-opacity:0.8" />
                  <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:0.6" />
                </linearGradient>
                <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#ff7700;stop-opacity:0.9" />
                  <stop offset="100%" style="stop-color:#ff4444;stop-opacity:0.8" />
                </linearGradient>
              </defs>
              
              <!-- 背景形状 -->
              <path d="M0,80 Q150,20 300,60 L300,200 L0,200 Z" fill="url(#bgGradient)" />
              
              <!-- 信用卡样式 -->
              <rect x="50" y="60" width="120" height="75" rx="8" fill="url(#cardGradient)" />
              <circle cx="70" cy="85" r="8" fill="#fff" opacity="0.8" />
              <rect x="60" y="100" width="40" height="3" rx="1" fill="#fff" opacity="0.6" />
              <rect x="60" y="108" width="60" height="3" rx="1" fill="#fff" opacity="0.6" />
              
              <!-- 装饰元素 -->
              <circle cx="220" cy="40" r="15" fill="#fff" opacity="0.3" />
              <circle cx="250" cy="120" r="20" fill="#fff" opacity="0.2" />
              
              <!-- 标签 -->
              <text x="75" y="25" font-size="12" fill="#666" text-anchor="middle">资产配置化</text>
              <text x="225" y="25" font-size="12" fill="#666" text-anchor="middle">产品持仓评估</text>
              <text x="50" y="170" font-size="12" fill="#666" text-anchor="middle">账户适配分析</text>
              <text x="250" y="170" font-size="12" fill="#666" text-anchor="middle">行业机会挖掘</text>
              <text x="150" y="190" font-size="12" fill="#666" text-anchor="middle">持仓异动监控</text>
              
              <!-- 人物剪影 -->
              <ellipse cx="40" cy="180" rx="8" ry="4" fill="#ff6b6b" opacity="0.6" />
              <rect x="36" y="165" width="8" height="15" rx="4" fill="#ff6b6b" opacity="0.8" />
              <circle cx="40" cy="160" r="4" fill="#ff6b6b" opacity="0.8" />
            </svg>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部按钮 -->
    <div class="bottom-action">
      <van-button 
        block 
        class="create-btn"
        @click="onCreate"
      >
        <van-icon name="plus" size="16" />
        创建投资账本
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

const onBack = () => {
  router.back()
}

const onCreate = () => {
  showToast('创建投资账本功能待实现')
}
</script>

<style scoped>
/* 页面容器 */
.diagnosis-page {
  width: 100%;
  height: 100vh;
  background: linear-gradient(180deg, #fff 0%, #fef7f0 100%);
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 导航栏 */
.nav-bar {
  background: #ff6b6b !important;
  color: #fff;
  flex-shrink: 0;
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: 5% 4%;
  overflow-y: auto;
  padding-bottom: 100px;
}

/* 模块通用样式 */
.diagnosis-section,
.wishlist-section {
  margin-bottom: 8%;
}

.section-header {
  margin-bottom: 6%;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #ff6b6b;
  margin: 0 0 3% 0;
  line-height: 1.3;
}

.section-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* 服务网格 */
.service-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4%;
  margin-bottom: 2%;
}

.service-item {
  display: flex;
  align-items: center;
  gap: 3%;
  padding: 3% 0;
  font-size: 14px;
  color: #333;
  font-weight: 400;
}

.service-item:nth-child(5) {
  grid-column: 1 / -1;
  justify-self: start;
}

/* 心愿账本可视化 */
.wishlist-visual {
  background: #fff;
  border-radius: 16px;
  padding: 4%;
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.1);
  margin-top: 4%;
}

.chart-container {
  width: 100%;
  height: 200px;
  position: relative;
  overflow: hidden;
  border-radius: 12px;
}

.wishlist-chart {
  width: 100%;
  height: 100%;
}

/* 底部按钮 */
.bottom-action {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 4% 4% 6% 4%;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
  border-top: 1px solid #f0f0f0;
}

.create-btn {
  background: #ff6b6b !important;
  border: none !important;
  border-radius: 8px !important;
  height: 48px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #fff !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 2% !important;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.create-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 4px rgba(255, 107, 107, 0.4);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .page-content {
    padding: 6% 5%;
  }
  
  .section-title {
    font-size: 22px;
  }
  
  .service-grid {
    gap: 3%;
  }
  
  .service-item {
    font-size: 13px;
  }
  
  .bottom-action {
    padding: 5% 5% 8% 5%;
  }
}

@media (min-width: 768px) {
  .page-content {
    max-width: 600px;
    margin: 0 auto;
    padding: 4% 6%;
  }
  
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .service-item:nth-child(5) {
    grid-column: 2 / 3;
    justify-self: center;
  }
}

/* 导航栏样式优化 */
:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}
</style>
