<template>
  <div class="diagnosis-page">
    <van-nav-bar
      title="我的持仓账本诊断"
      left-arrow
      @click-left="onBack"
      right-text="..."
      class="nav-bar"
    />
    
    <div class="page-content">
      <!-- 标签页导航 -->
      <div class="tabs-container">
        <van-tabs 
          v-model:active="activeTab" 
          class="diagnosis-tabs"
          color="#ff6b6b"
          title-active-color="#ff6b6b"
          title-inactive-color="#999"
        >
          <van-tab title="资产配置" name="asset">
            <!-- 资产大类分布 -->
            <div class="analysis-section">
              <div class="section-header">
                <h3 class="section-title">
                  资产大类分布
                  <van-icon name="question-o" size="14" color="#999" />
                </h3>
                <div class="toggle-switch">
                  <span class="toggle-label">持仓穿透</span>
                  <van-switch v-model="showPenetration" size="20" />
                </div>
              </div>
              
              <!-- 饼图区域 -->
              <div class="chart-container">
                <div class="pie-chart">
                  <svg width="120" height="120" viewBox="0 0 120 120">
                    <!-- 股票 60% -->
                    <path d="M 60,60 L 60,10 A 50,50 0 1,1 95,85 Z" fill="#4CAF50" />
                    <!-- 债券 30% -->
                    <path d="M 60,60 L 95,85 A 50,50 0 0,1 25,85 Z" fill="#2196F3" />
                    <!-- 商品 7% -->
                    <path d="M 60,60 L 25,85 A 50,50 0 0,1 15,65 Z" fill="#FF9800" />
                    <!-- 其他 3% -->
                    <path d="M 60,60 L 15,65 A 50,50 0 0,1 60,10 Z" fill="#9E9E9E" />
                  </svg>
                </div>
                
                <div class="legend">
                  <div class="legend-item">
                    <span class="legend-dot" style="background: #4CAF50;"></span>
                    <span class="legend-label">股票</span>
                    <span class="legend-value">60.00%</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-dot" style="background: #2196F3;"></span>
                    <span class="legend-label">债券</span>
                    <span class="legend-value">30.00%</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-dot" style="background: #FF9800;"></span>
                    <span class="legend-label">商品</span>
                    <span class="legend-value">7.00%</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-dot" style="background: #9E9E9E;"></span>
                    <span class="legend-label">其他</span>
                    <span class="legend-value">3.00%</span>
                  </div>
                </div>
              </div>
              
              <!-- 分析文本 -->
              <div class="analysis-text">
                <p><strong>股票类资产占比过高</strong></p>
                <p>当前股票占比60%，债券资产占比30%，建议适当降低股票类资产占比，增加债券类资产配置，当前风险水平的投资组合可能会承受过度的承受能力的风险。</p>
              </div>
            </div>
          </van-tab>
          
          <van-tab title="收益体验" name="return">
            <div class="analysis-section">
              <p class="tab-placeholder">收益体验分析内容</p>
            </div>
          </van-tab>
          
          <van-tab title="风险能力" name="risk">
            <div class="analysis-section">
              <p class="tab-placeholder">风险能力分析内容</p>
            </div>
          </van-tab>
          
          <van-tab title="基金诊断" name="fund">
            <div class="analysis-section">
              <p class="tab-placeholder">基金诊断分析内容</p>
            </div>
          </van-tab>
        </van-tabs>
      </div>
      
      <!-- 行业分析 -->
      <div class="industry-section">
        <div class="section-header">
          <h3 class="section-title">
            行业分析
            <van-icon name="question-o" size="14" color="#999" />
          </h3>
          <div class="view-toggle">
            <span class="toggle-text">顶风险</span>
            <van-icon name="arrow-up" size="12" color="#999" />
            <span class="toggle-text">前五最新配置</span>
          </div>
        </div>
        
        <div class="industry-cards">
          <div class="industry-card">
            <div class="card-content">
              <div class="industry-name">交通运输</div>
              <div class="industry-stats">
                <div class="stat-item">
                  <span class="stat-label">占比52.16%</span>
                  <span class="stat-change negative">近1月 -0.94%</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="industry-card">
            <div class="card-content">
              <div class="industry-name">机械设备</div>
              <div class="industry-stats">
                <div class="stat-item">
                  <span class="stat-label">占比21.97%</span>
                  <span class="stat-change positive">近1月 +4.24%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="industry-detail">
          <div class="detail-header">
            <van-icon name="minus" size="16" color="#333" />
            <span class="detail-title">交通运输</span>
            <span class="detail-subtitle">行业股票占比重的几个基金有</span>
          </div>
          
          <div class="fund-list">
            <div class="fund-item">
              <span class="fund-name">易方达消费行业</span>
              <div class="fund-progress">
                <div class="progress-bar">
                  <div class="progress-fill" style="width: 52%;"></div>
                </div>
                <span class="progress-text">52.16%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 当前激活的标签页
const activeTab = ref('asset')

// 持仓穿透开关
const showPenetration = ref(false)

const onBack = () => {
  router.back()
}
</script>

<style scoped>
.diagnosis-page {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.nav-bar {
  background: #ff6b6b !important;
  color: #fff;
  flex-shrink: 0;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}

.tabs-container {
  background: #fff;
  margin-bottom: 10px;
}

.diagnosis-tabs {
  background: #fff;
}

.analysis-section {
  padding: 16px;
  background: #fff;
  margin-bottom: 10px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.toggle-switch {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-label {
  font-size: 14px;
  color: #666;
}

.chart-container {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
}

.pie-chart {
  flex-shrink: 0;
}

.legend {
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-label {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.legend-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.analysis-text {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.analysis-text p {
  margin: 0 0 8px 0;
}

.tab-placeholder {
  text-align: center;
  color: #999;
  padding: 40px 0;
  margin: 0;
}

.industry-section {
  background: #fff;
  margin-bottom: 10px;
}

.view-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.toggle-text {
  font-size: 12px;
}

.industry-cards {
  padding: 0 16px;
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.industry-card {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
}

.industry-card:first-child .card-content {
  background: linear-gradient(135deg, #ffcccb, #ffb3ba);
}

.industry-card:last-child .card-content {
  background: linear-gradient(135deg, #c8e6c9, #a5d6a7);
}

.card-content {
  padding: 12px;
  color: #333;
}

.industry-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 12px;
  display: block;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
}

.stat-change.negative {
  color: #f56c6c;
}

.stat-change.positive {
  color: #67c23a;
}

.industry-detail {
  padding: 0 16px 16px;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.detail-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.detail-subtitle {
  font-size: 12px;
  color: #666;
}

.fund-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.fund-name {
  font-size: 14px;
  color: #333;
  min-width: 80px;
}

.fund-progress {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #333;
  border-radius: 2px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 40px;
}

:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__text) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}

:deep(.van-tabs__nav) {
  background: #fff;
}

:deep(.van-tab) {
  font-size: 14px;
}
</style>
