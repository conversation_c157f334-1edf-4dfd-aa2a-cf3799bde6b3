<template>
  <div class="account-list-page">
    <van-nav-bar
      title="账本列表"
      class="nav-bar"
    >
      <template #right>
        <div class="nav-actions">
          <van-icon
            name="chart-trending-o"
            size="20"
            color="#fff"
            class="nav-icon"
            @click="goToDiagnosis"
          />
          <span class="nav-text" @click="onAddAccount">添加</span>
        </div>
      </template>
    </van-nav-bar>
    
    <div class="account-content">
      <div class="account-grid">
        <div 
          v-for="account in accountList" 
          :key="account.id"
          class="account-item"
          @click="goToAccount(account)"
        >
          <div class="account-icon">
            <van-icon :name="account.icon" size="32" color="#ff6b6b" />
          </div>
          <div class="account-info">
            <div class="account-name">{{ account.name }}</div>
            <div class="account-desc">{{ account.description }}</div>
            <div class="account-amount">
              <span class="amount-label">总资产</span>
              <span class="amount-value">¥{{ formatAmount(account.totalAmount) }}</span>
            </div>
          </div>
          <van-icon name="arrow" size="16" color="#c8c9cc" />
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="accountList.length === 0" class="empty-state">
        <div class="empty-icon">
          <van-icon name="records" size="64" color="#dcdee0" />
        </div>
        <div class="empty-text">暂无账本</div>
        <van-button 
          type="primary" 
          size="small" 
          class="add-btn"
          @click="onAddAccount"
        >
          创建第一个账本
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

// 模拟账本数据
const accountList = ref([
  {
    id: 1,
    name: '股票投资账本',
    description: '记录股票投资收益',
    icon: 'chart-trending-o',
    totalAmount: 125680.50,
    createTime: '2024-01-15'
  },
  {
    id: 2,
    name: '基金定投账本',
    description: '基金定投记录',
    icon: 'gold-coin-o',
    totalAmount: 89320.00,
    createTime: '2024-02-01'
  },
  {
    id: 3,
    name: '理财产品账本',
    description: '银行理财产品收益',
    icon: 'balance-o',
    totalAmount: 56780.25,
    createTime: '2024-02-10'
  }
])

// 格式化金额
const formatAmount = (amount) => {
  return amount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 跳转到账本详情
const goToAccount = (account) => {
  router.push({
    name: 'AccountDetail',
    params: { id: account.id },
    query: { name: account.name }
  })
}

// 跳转到诊断页面
const goToDiagnosis = () => {
  router.push({ name: 'AccountDiagnosis' })
}

// 添加账本
const onAddAccount = () => {
  showToast('添加账本功能待实现')
}
</script>

<style scoped>
/* 页面容器 */
.account-list-page {
  width: 100%;
  height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.nav-bar {
  background: #ff6b6b !important;
  color: #fff;
  flex-shrink: 0;
}

/* 导航栏右侧操作区 */
.nav-actions {
  display: flex;
  align-items: center;
  gap: 4%;
}

.nav-icon {
  padding: 2%;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.nav-icon:active {
  opacity: 0.7;
}

.nav-text {
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.nav-text:active {
  opacity: 0.7;
}

/* 内容区域 */
.account-content {
  flex: 1;
  padding: 4% 4% 6% 4%;
  overflow-y: auto;
}

/* 账本网格 */
.account-grid {
  display: flex;
  flex-direction: column;
  gap: 3%;
}

/* 账本项目 */
.account-item {
  background: #fff;
  border-radius: 12px;
  padding: 4%;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.account-item:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 账本图标 */
.account-icon {
  width: 48px;
  height: 48px;
  background: #fff2f0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4%;
  flex-shrink: 0;
}

/* 账本信息 */
.account-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2%;
}

.account-name {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  line-height: 1.4;
}

.account-desc {
  font-size: 13px;
  color: #969799;
  line-height: 1.3;
}

.account-amount {
  display: flex;
  align-items: center;
  gap: 2%;
  margin-top: 1%;
}

.amount-label {
  font-size: 12px;
  color: #969799;
}

.amount-value {
  font-size: 15px;
  font-weight: 600;
  color: #ff6b6b;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15% 8%;
  text-align: center;
}

.empty-icon {
  margin-bottom: 6%;
}

.empty-text {
  font-size: 16px;
  color: #969799;
  margin-bottom: 8%;
}

.add-btn {
  background: #ff6b6b !important;
  border: none !important;
  border-radius: 20px !important;
  padding: 0 6% !important;
  height: 36px !important;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .account-content {
    padding: 5% 5% 8% 5%;
  }
  
  .account-item {
    padding: 5%;
  }
  
  .account-name {
    font-size: 15px;
  }
  
  .amount-value {
    font-size: 14px;
  }
}

@media (min-width: 768px) {
  .account-content {
    padding: 3% 6% 4% 6%;
    max-width: 600px;
    margin: 0 auto;
  }
}

/* 深度选择器优化导航栏样式 */
:deep(.van-nav-bar__title) {
  color: #fff !important;
}

:deep(.van-nav-bar__text) {
  color: #fff !important;
}

:deep(.van-nav-bar__arrow) {
  color: #fff !important;
}
</style>
